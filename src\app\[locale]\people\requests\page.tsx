import React, { Suspense } from "react";
import { ApprovalRequestsTable } from "../../_modules/people/_components/approval-requests/table";

const Requests = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <RequestsHeader title="جميع طلبات الموظفين" />
      <Suspense fallback={<div>Loading...</div>}>
        <ApprovalRequestsTable
          page={parseInt(params.page || "1")}
          limit={parseInt(params.limit || "10")}
        />
      </Suspense>
    </>
  );
};

export default Requests;

const RequestsHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="max-w-[301px] leading-[120%] text-base text-secondary mb-0.5">
      {title}
    </h2>
  );
};
