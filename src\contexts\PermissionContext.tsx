"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
} from "react";
import { Permission } from "@/types/auth";
import { useUserPermission } from "@/app/[locale]/_modules/people/hooks/user/useUserPermission";
import {
  saveDataToSessionStorage,
  getDataFromSessionStorage,
} from "@/lib/local-storage";
import { useSystem } from "./system-provider";

interface PermissionContextType {
  hasPermission: (permission: string) => boolean;
  permissions: Permission[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => void;
  clearPermissions: () => void;
}

const PermissionContext = createContext<PermissionContextType | null>(null);

export const PermissionProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const {
    permission: apiPermissions,
    isLoading,
    error,
    mutate,
  } = useUserPermission();
  const { currSystem } = useSystem();

  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isPermissionsLoaded, setIsPermissionsLoaded] = useState(false);
  // Create system-specific storage key
  const STORAGE_KEY = `user_permissions_${currSystem}`;

  // Clear permissions when system changes
  useEffect(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    // Clear all system-specific permission storage when system changes
    if (typeof window !== "undefined") {
      const systems = ["people", "procure", "cm", "core"];
      systems.forEach((system) => {
        if (system !== currSystem) {
          sessionStorage.removeItem(`user_permissions_${system}`);
        }
      });
    }
  }, [currSystem]);

  // Load permissions from session storage for current system
  useEffect(() => {
    if (currSystem && currSystem !== "core") {
      const storedPermissions =
        getDataFromSessionStorage<Permission[]>(STORAGE_KEY);
      if (storedPermissions && storedPermissions.length > 0) {
        setPermissions(storedPermissions);
        setIsPermissionsLoaded(true);
      }
    }
  }, [currSystem, STORAGE_KEY]);

  // Clear permissions from both state and session storage
  const clearPermissions = useCallback(() => {
    setPermissions([]);
    setIsPermissionsLoaded(false);
    if (typeof window !== "undefined") {
      sessionStorage.removeItem(STORAGE_KEY);
    }
  }, [STORAGE_KEY]);

  // Update permissions when API data changes
  useEffect(() => {
    if (apiPermissions && apiPermissions.length > 0) {
      saveDataToSessionStorage(STORAGE_KEY, apiPermissions);
      setPermissions(apiPermissions);
      setIsPermissionsLoaded(true);
    }
  }, [apiPermissions, STORAGE_KEY]);

  const hasPermission = useCallback(
    (perm: string) => {
      return permissions.some((p) => p.id === perm);
    },
    [permissions],
  );

  const shouldRenderChildren = isPermissionsLoaded || error || (currSystem === "core") || (!isLoading && !apiPermissions);

  return (
    <PermissionContext.Provider
      value={{
        permissions,
        hasPermission,
        isLoading,
        error,
        refetch: mutate,
        clearPermissions,
      }}
    >
      {shouldRenderChildren ? children : null}
    </PermissionContext.Provider>
  );
};

export const usePermission = (): PermissionContextType => {
  const ctx = useContext(PermissionContext);
  if (!ctx)
    throw new Error("usePermission must be used within PermissionProvider");
  return ctx;
};
