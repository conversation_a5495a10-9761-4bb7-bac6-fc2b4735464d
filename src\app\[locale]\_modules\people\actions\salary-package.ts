"use server";

import { isValidationSuccess, validateFormData } from "@/lib/form-utils";
import { handleError } from "@/lib/utils";
import { ActionState } from "@/types";
import {
  SalaryPackageSchemaType,
  salaryPackageSchema,
} from "../schemas/salaryPackageSchema";
import * as z from "zod";
import { TSalaryPackageResponse } from "../type/salary-package";
import { peopleService } from "@/services/api/people";

export const saveSalaryPackage = async (
  _prevState: ActionState<TSalaryPackageResponse>,
  data: FormData,
): Promise<ActionState<TSalaryPackageResponse>> => {
  try {
    // Determine if we're updating based on whether an ID is present in the form data
    const isUpdate = data.has("id") && data.get("id") !== "";
    const mode = isUpdate ? "update" : "create";

    // Validate the form data using the appropriate schema
    const validation = await validateFormData<SalaryPackageSchemaType>(
      data,
      (t) => salaryPackageSchema(t, mode) as z.ZodType<SalaryPackageSchemaType>,
    );

    if (!isValidationSuccess(validation)) {
      return {
        error: validation.error,
        issues: validation.issues,
      };
    }
    // Use the service to create/update the salary package
    const response = await peopleService.saveSalaryPackage(data);

    // Set success message based on whether we're creating or updating
    const successMessage = isUpdate
      ? "Salary package updated successfully 😊"
      : "Salary package created successfully 😊";

    return {
      success: successMessage,
      error: "",
      issues: [],
      data: response,
    };
  } catch (err) {
    return handleError(
      err,
      "An error occurred while saving the salary package",
    );
  }
};
