"use client";

import React, {
  useRef,
  startTransition,
  useState,
  useEffect,
  useMemo,
} from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useActionState } from "react";
import { useTranslations } from "next-intl";

import { Loader } from "lucide-react";
import { PAGES } from "@/enums";
import { ActionState, TinputField } from "@/types";
import {
  EmployeeSchemaType,
  employeeSchema,
} from "../../../schemas/employeeSchema";
import useFormFields from "@/hooks/useFormFields";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import { addEmployee } from "../../../actions/add-employee";
import { TEmployee } from "../../../type/employee";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useEmployees } from "../../../hooks/employees/useEmployees";
import { useSearchParams } from "next/navigation";

export default function AddEmployeeForm() {
  const t = useTranslations();
  const formRef = useRef<HTMLFormElement | null>(null);
  const [selectKey, setSelectKey] = useState<number>(0);
  const { getFormFields } = useFormFields({ formType: PAGES.ADDEMPLOYEE, t });
  const { showToast } = useToastMessage();
  const searchParams = useSearchParams();
  const page = searchParams.get("page") || "1";
  const limit = searchParams.get("limit") || "3";
  const { mutate: mutateEmployeesList } = useEmployees(
    Number(page),
    Number(limit),
  );
  const initialState: ActionState<TEmployee> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  const [state, submitAction, isPending] = useActionState(
    addEmployee,
    initialState,
  );

  const defaultFormValues = useMemo(
    () => ({
      name: "",
      email: "",
      phone: "",
      department: "hr" as const,
      start_date: new Date(),
      assignments: [],
      attachments: [],
      avatar: undefined,
    }),
    [],
  );

  const form = useForm<EmployeeSchemaType>({
    resolver: zodResolver(employeeSchema(t)),
    defaultValues: defaultFormValues,
    mode: "all",
  });

  useEffect(() => {
    if (state?.success) {
      showToast("success", state.success);
      form.reset(defaultFormValues);
      mutateEmployeesList();

      setTimeout(() => {
        setSelectKey((prev) => prev + 1);
      }, 0);
    }
  }, [state, form, defaultFormValues, mutateEmployeesList]);

  useEffect(() => {
    if (state?.error) {
      showToast("error", state.issues![0] || state.error);
    }
  }, [state]);

  const handleSubmit = (data: EmployeeSchemaType) => {
    const formData = new FormData();
    formData.append("name", data.name);
    formData.append("email", data.email);
    formData.append("phone", data.phone);
    formData.append("department", data.department);

    if (data.start_date) {
      // Just send the date as a string - the schema will handle conversion
      formData.append("start_date", data.start_date.toISOString());
    }
    if (data.avatar instanceof File) {
      formData.append("avatar", data.avatar);
    }

    // Handle assignments using user_roles_list format
    if (data.assignments && data.assignments.length > 0) {
      data.assignments.forEach((assignment, index) => {
        // Append role_id and project_id separately using the user_roles_list format
        formData.append(
          `employee[user_roles_list][${index}][role_id]`,
          assignment.role,
        );
        formData.append(
          `employee[user_roles_list][${index}][project_id]`,
          assignment.project,
        );
      });
    }

    if (data.attachments && data.attachments.length > 0) {
      data.attachments.forEach((file) => {
        formData.append(`attachments[]`, file);
      });
    }

    startTransition(() => {
      submitAction(formData);
    });
  };

  return (
    <div className="bg-white rounded-lg max-w-2xl mx-auto w-[99.5%]">
      <Form {...form}>
        <form
          ref={formRef}
          action={submitAction}
          onSubmit={form.handleSubmit(handleSubmit)}
          className="space-y-6"
        >
          {(getFormFields() as TinputField<EmployeeSchemaType>[]).map(
            (fieldConfig) => (
              <FormFieldRenderer
                key={`${fieldConfig.name?.toString()}-${
                  fieldConfig.type === "select" ? selectKey : ""
                }`}
                fieldConfig={fieldConfig}
                form={form}
                isPending={isPending}
              />
            ),
          )}

          <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-[100%] rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
            <Button
              disabled={isPending || !form.formState.isValid}
              type="submit"
              className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
            >
              {isPending ? (
                <Loader className="animate-spin" />
              ) : (
                t("common.buttonText.add")
              )}
            </Button>
            <Button
              variant={"outline"}
              type="button"
              className="w-full h-12 sm:max-w-[244px] rounded-lg"
              onClick={() => {
                form.reset(defaultFormValues);
                // Force a re-render to ensure all fields are cleared
                setTimeout(() => {
                  setSelectKey((prev) => prev + 1);
                }, 0);
              }}
            >
              {t("common.buttonText.cancel2")}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}
