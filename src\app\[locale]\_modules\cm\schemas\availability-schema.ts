import { z } from "zod";
import { TFunction } from "@/types";

export const availabilitySchema = (t: TFunction) => {
  return z.object({
    availability: z.array(
      z.object({
        day: z.string(),
        isEnabled: z.boolean(),
        intervals: z.array(
          z.object({
            from: z
              .string()
              .regex(
                /^([01]\d|2[0-3]):([0-5]\d)$/,
                t("common.Error.time.format"),
              ),
            to: z
              .string()
              .regex(
                /^([01]\d|2[0-3]):([0-5]\d)$/,
                t("common.Error.time.format"),
              ),
          }),
        ),
      }),
    ),
  });
};
