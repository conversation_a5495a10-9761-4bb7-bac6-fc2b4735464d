import { z } from "zod";
import { TFunction } from "@/types";
import { parse } from "date-fns";

export const salaryPackageSchema = (
  t: TFunction,
  mode: "create" | "update" = "create",
) => {
  // Base schema with common validations
  const baseSchema = z.object({
    employee_id: z.string({
      required_error: t("common.form.employee.error.required"),
    }),

    base_salary: z
      .string()
      .min(1, { message: t("common.form.salary.base_salary.required") })
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      }),

    housing_allowance: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .default("0"),

    transportation_allowance: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .default("0"),

    other_allowances: z
      .string()
      .refine((val) => !isNaN(Number(val.replace(/,/g, ""))), {
        message: t("common.form.verifyCode.error.notNumber"),
      })
      .default("0"),

    notes: z
      .string()
      .min(1, { message: t("common.form.salary.notes.required") }),
  });

  if (mode === "create") {
    return baseSchema.extend({
      effective_date: z.preprocess(
        // Convert string to Date if needed
        (val) => {
          if (typeof val === "string") {
            // Try to parse as a date-fns formatted string (dd-MM-yyyy)
            try {
              const parsedDate = parse(val, "dd-MM-yyyy", new Date());
              if (!isNaN(parsedDate.getTime())) {
                return parsedDate;
              }
            } catch (e) {
              // If parsing fails, try standard date parsing
            }

            // Standard date parsing
            const standardDate = new Date(val);
            if (!isNaN(standardDate.getTime())) {
              return standardDate;
            }

            // If all parsing fails, return invalid date to trigger validation error
            return new Date("invalid date");
          }
          return val;
        },
        // Then validate as date
        z.date({
          required_error: t("common.form.date.error.required"),
          invalid_type_error: t("common.form.date.error.invalid"),
        }),
      ),
    });
  }

  // For update mode, accept any valid date
  return baseSchema.extend({
    effective_date: z.preprocess(
      // Convert string to Date if needed
      (val) => {
        if (typeof val === "string") {
          // Try to parse as a date-fns formatted string (dd-MM-yyyy)
          try {
            const parsedDate = parse(val, "dd-MM-yyyy", new Date());
            if (!isNaN(parsedDate.getTime())) {
              return parsedDate;
            }
          } catch (e) {
            // If parsing fails, try standard date parsing
          }

          // Standard date parsing
          const standardDate = new Date(val);
          if (!isNaN(standardDate.getTime())) {
            return standardDate;
          }

          // If all parsing fails, return today's date
          return new Date();
        }
        return val;
      },
      // Then validate as date
      z.date({
        required_error: t("common.form.date.error.required"),
        invalid_type_error: t("common.form.date.error.invalid"),
      }),
    ),
  });
};

export type SalaryPackageSchemaType = z.infer<
  ReturnType<typeof salaryPackageSchema>
>;
