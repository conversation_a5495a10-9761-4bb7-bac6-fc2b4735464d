import { <PERSON><PERSON> } from "@/components/ui/button";
import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { LeaveDetail } from "../../../../type/employee-leaves";
import { useState } from "react";
import { UpdateDatesModal } from "./update-dates-modal";
import { X, Loader } from "lucide-react";
import {
  Edit,
  RightCircle,
} from "../../../../../../../../../public/images/icons";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Locale } from "@/i18n/routing";
import { useLocale } from "next-intl";
import { LANGUAGES } from "@/constants/enum";
import { CiMenuKebab } from "react-icons/ci";

type EmployeeLeavesActionsProps<TData> = {
  row: Row<TData>;
  onWithdrawLeave?: (leaveId: string) => void;
  onUpdateDates?: (
    leaveId: string,
    startDate: Date,
    endDate: Date,
    leaveDuration?: string,
    leaveType?: string,
  ) => void;
  onAcceptLeave?: (leaveId: string) => void;
  onRejectLeave?: (leaveId: string) => void;
  isWithdrawing?: boolean;
  isUpdatingDates?: boolean;
  isAccepting?: boolean;
  isRejecting?: boolean;
};

const EmployeeLeavesActions = <TData extends LeaveDetail>({
  row,
  onWithdrawLeave,
  onUpdateDates,
  onAcceptLeave,
  onRejectLeave,
  isWithdrawing,
  isUpdatingDates,
  isAccepting,
  isRejecting,
}: EmployeeLeavesActionsProps<TData>) => {
  const t = useTranslations();
  const locale: Locale = useLocale() as Locale;
  const isAr = locale == LANGUAGES.ARABIC;
  // State for the update dates modal
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

  // Get the leave details
  const leave = row.original;
  const startDate = leave.attributes.start_date;
  const endDate = leave.attributes.end_date;
  const status = leave.attributes.status;
  const leaveType = leave.attributes.leave_type;
  const leaveDuration = leave.attributes.leave_duration;

  // Check if the end date hasn't expired
  const isEndDateValid = endDate ? new Date(endDate) >= new Date() : false;

  const canWithdraw =
    status === "waiting" ||
    (status === "pending" && isEndDateValid) ||
    (status === "approved" && isEndDateValid);

  const canUpdateDates =
    status === "waiting" || (status === "pending" && isEndDateValid);

  const canAccept = status === "waiting" || status === "pending";

  const canReject = status === "waiting" || status === "pending";

  const handleUpdateDates = (newStartDate: Date, newEndDate: Date) => {
    if (onUpdateDates) {
      onUpdateDates(
        leave.id,
        newStartDate,
        newEndDate,
        leaveDuration,
        leaveType,
      );
      setIsUpdateModalOpen(false);
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            disabled={
              status === "approved" ||
              status === "rejected" ||
              status === "withdrawn"
            }
            variant="outline"
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Open menu</span>
            <CiMenuKebab />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          align={isAr ? "start" : "end"}
          className="w-48 rounded-xl"
        >
          {/* Accept Action */}
          {canAccept && (
            <DropdownMenuItem
              onClick={() => {
                if (onAcceptLeave && !isAccepting) {
                  onAcceptLeave(leave.id);
                }
              }}
              disabled={isAccepting}
              className="text-gray-500 focus:text-green-600 flex-row-reverse justify-start"
            >
              {isAccepting ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <RightCircle className="ms-2 !h-6 !w-6" />
              )}
              {t("people.leaves-requests-component.actions.approve")}
            </DropdownMenuItem>
          )}

          {/* Reject Action */}
          {canReject && (
            <DropdownMenuItem
              onClick={() => {
                if (onRejectLeave && !isRejecting) {
                  onRejectLeave(leave.id);
                }
              }}
              disabled={isRejecting}
              className="text-gray-500 focus:text-red-600 flex-row-reverse justify-start"
            >
              {isRejecting ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <X className="ms-2 !h-6 !w-6" />
              )}
              {t("people.leaves-requests-component.actions.reject")}
            </DropdownMenuItem>
          )}

          {/* Update Dates Action */}
          {canUpdateDates && (
            <DropdownMenuItem
              onClick={() => {
                if (!isUpdatingDates) {
                  setIsUpdateModalOpen(true);
                }
              }}
              disabled={isUpdatingDates}
              className="text-gray-500 focus:text-blue-600 flex-row-reverse justify-start"
            >
              {isUpdatingDates ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <Edit className="ms-2 !h-6 !w-6" />
              )}
              {t(
                "people.employees-page.profile.leaves.update-dates.update-button",
              )}
            </DropdownMenuItem>
          )}

          {/* Withdraw Action */}
          {canWithdraw && (
            <DropdownMenuItem
              onClick={() => {
                if (onWithdrawLeave && !isWithdrawing) {
                  onWithdrawLeave(leave.id);
                }
              }}
              disabled={isWithdrawing}
              className="text-gray-600 focus:text-orange-600 flex-row-reverse justify-start"
            >
              {isWithdrawing ? (
                <Loader className="animate-spin ms-2 !h-6 !w-6" />
              ) : (
                <X className="ms-2 !h-6 !w-6" />
              )}
              {t("people.employees-page.profile.leaves.table.actions.withdraw")}
            </DropdownMenuItem>
          )}

          {/* Show status for approved/rejected requests */}
          {(status === "approved" || status === "rejected") &&
            !canAccept &&
            !canReject && (
              <DropdownMenuItem disabled className="text-gray-500">
                {status === "approved"
                  ? t("people.leaves-requests-page.actions.accepted")
                  : t("people.leaves-requests-page.actions.rejected")}
              </DropdownMenuItem>
            )}
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Date picker modal */}
      <UpdateDatesModal
        isOpen={isUpdateModalOpen}
        onClose={() => setIsUpdateModalOpen(false)}
        onUpdate={handleUpdateDates}
        initialStartDate={startDate}
        initialEndDate={endDate}
        isLoading={isUpdatingDates || false}
      />
    </>
  );
};

export { EmployeeLeavesActions };
