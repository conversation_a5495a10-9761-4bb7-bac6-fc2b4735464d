"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { PAGES } from "@/enums";
import useFormFields from "@/hooks/useFormFields";
import { zodResolver } from "@hookform/resolvers/zod";
import { Calendar, Loader } from "lucide-react";
import React, {
  startTransition,
  useActionState,
  useRef,
  useState,
  useEffect,
} from "react";
import { useForm } from "react-hook-form";
import { useTranslations } from "next-intl";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import ResponsiveDialog from "@/components/responsive-dialog";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import { useToastMessage } from "@/hooks/use-toast-message";
import { createLeave } from "../../../../actions/leaves";
import {
  createLeaveSchema,
  CreateLeaveSchemaType,
} from "../../../../schemas/leaveSchema";
import { ActionState, TFunction, TinputField } from "@/types";
import { UpdateDatesModal } from "./update-dates-modal";
import { formatDate } from "@/lib/dateFormatter";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";
import { LEAVE_DURATION, LEAVE_TYPE } from "../../../../enum";

type CreateLeaveModalProps = {
  isOpen: boolean;
  onClose: () => void;
  employeeId: string;
  onSuccess?: () => void;
};

export const CreateLeaveModal = ({
  isOpen,
  onClose,
  employeeId,
  onSuccess,
}: CreateLeaveModalProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale() as Locale;
  const { getFormFields } = useFormFields({ formType: PAGES.CREATELEAVE, t });
  const formRef = useRef<HTMLFormElement | null>(null);
  const { showToast } = useToastMessage();

  // State for date picker modal
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Initialize action state for leave creation
  const initialState: ActionState<{ id: string; status: string }> = {
    error: "",
    success: "",
    issues: [],
    data: null,
  };

  // Bind the action to the form data
  const createLeaveAction = async (
    prevState: typeof initialState,
    formData: FormData,
  ) => {
    return createLeave(employeeId, prevState, formData);
  };

  const [state, submitAction, isPending] = useActionState(
    createLeaveAction,
    initialState,
  );

  // Initialize form
  const form = useForm<CreateLeaveSchemaType>({
    resolver: zodResolver(createLeaveSchema(t)),
    defaultValues: {
      leave_type: LEAVE_TYPE.ANNUAL,
      leave_duration: LEAVE_DURATION.FULL_DAY,
      start_date: new Date(),
      end_date: new Date(),
      reason: "",
      documents: [],
    },
    mode: "all",
  });

  // Handle success and error states
  useEffect(() => {
    if (state.success) {
      showToast(
        "success",
        t("people.employees-page.profile.leaves.create-leave-modal.success"),
      );
      form.reset({
        leave_type: LEAVE_TYPE.ANNUAL,
        leave_duration: LEAVE_DURATION.FULL_DAY,
        start_date: new Date(),
        end_date: new Date(),
        reason: "",
        documents: [],
      });
      onClose();
      if (onSuccess) {
        onSuccess();
      }
    } else if (state.error) {
      showToast("error", state.error);
    }
  }, [state]);

  // Handle date selection from the date picker
  const handleDateSelection = (startDate: Date, endDate: Date) => {
    form.setValue("start_date", startDate);
    form.setValue("end_date", endDate);
    setIsDatePickerOpen(false);
  };

  // Open date picker modal
  const openDatePicker = () => {
    setIsDatePickerOpen(true);
  };

  // Submit form
  const handleSubmit = (data: CreateLeaveSchemaType) => {
    const formData = new FormData();

    // Add form fields
    formData.append("leave_type", data.leave_type);

    formData.append("leave_duration", data.leave_duration);

    if (data.start_date) {
      formData.append("start_date", data.start_date.toString());
    }

    if (data.end_date) {
      formData.append("end_date", data.end_date.toString());
    }

    // Only append reason if it's provided and not empty
    if (data.reason && data.reason.trim().length > 0) {
      formData.append("reason", data.reason.trim());
    }

    if (data.documents && data.documents.length > 0) {
      data.documents.forEach((file) => {
        formData.append(`documents[]`, file);
      });
    }

    startTransition(() => {
      submitAction(formData);
    });
  };

  // Custom date field component
  const DateRangeField = () => {
    const startDate = form.watch("start_date");
    const endDate = form.watch("end_date");

    return (
      <div className="space-y-2">
        <div className="text-sm font-medium">
          {t(
            "people.employees-page.profile.leaves.create-leave-modal.start-date",
          )}{" "}
          -
          {t(
            "people.employees-page.profile.leaves.create-leave-modal.end-date",
          )}
        </div>
        <Button
          type="button"
          variant="outline"
          className="w-full justify-start text-left font-normal h-10 px-3 py-2"
          onClick={openDatePicker}
        >
          <Calendar className="mr-2 h-4 w-4" />
          <span>
            {startDate && endDate
              ? `${formatDate(startDate, locale)} - ${formatDate(
                  endDate,
                  locale,
                )}`
              : t("people.employees-page.profile.leaves.update-dates.none")}
          </span>
        </Button>
      </div>
    );
  };

  // Filter out the date fields from the form fields
  const filteredFormFields = (
    getFormFields() as TinputField<CreateLeaveSchemaType>[]
  ).filter((field) => field.name !== "start_date" && field.name !== "end_date");

  return (
    <>
      <ResponsiveDialog
        open={isOpen}
        onOpenChange={(open) => {
          if (!open) onClose();
        }}
        className="h-[93vh]"
        header={
          <>
            <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px]">
              {t(
                "people.employees-page.profile.leaves.create-leave-modal.title",
              )}
            </DialogTitle>
            <DialogDescription className="sr-only">
              {t(
                "people.employees-page.profile.leaves.create-leave-modal.description",
              )}
            </DialogDescription>
          </>
        }
      >
        <div className="max-h-[calc(93vh-65px)] !h-full">
          <Form {...form}>
            <form
              ref={formRef}
              onSubmit={form.handleSubmit(handleSubmit)}
              className="flex justify-between flex-col gap-6 min-h-[calc(93vh-125px)]"
            >
              {/* Date Range Field */}
              <DateRangeField />
              {/* Other form fields */}
              <div className="flex-1 h-full flex flex-col gap-6">
                {filteredFormFields.map((fieldConfig) => (
                  <FormFieldRenderer
                    key={`${fieldConfig.name?.toString()}`}
                    fieldConfig={fieldConfig}
                    form={form}
                    isPending={isPending}
                  />
                ))}
              </div>

              {/* Submit buttons */}
              <div className="sticky border-t border-t-slate-200 pt-4 bottom-0 left-0 w-full rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
                <Button
                  disabled={isPending}
                  type="submit"
                  className="px-4 py-2 w-full h-12 sm:max-w-[244px] rounded-lg"
                >
                  {isPending ? (
                    <Loader className="animate-spin" />
                  ) : (
                    t(
                      "people.employees-page.profile.leaves.create-leave-modal.submit",
                    )
                  )}
                </Button>
                <Button
                  variant="outline"
                  type="button"
                  className="w-full h-12 sm:max-w-[244px] rounded-lg"
                  onClick={onClose}
                  disabled={isPending}
                >
                  {t("common.buttonText.cancel")}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </ResponsiveDialog>

      {/* Date Picker Modal */}
      <UpdateDatesModal
        isOpen={isDatePickerOpen}
        onClose={() => setIsDatePickerOpen(false)}
        onUpdate={handleDateSelection}
        initialStartDate={form.getValues("start_date")}
        initialEndDate={form.getValues("end_date")}
        isLoading={false}
        mode="create"
      />
    </>
  );
};
