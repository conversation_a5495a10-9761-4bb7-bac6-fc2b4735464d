import React from "react";
import { UseFormReturn } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import FormFieldRenderer from "@/components/auth/fields/form-field-renderer";
import FormattedCurrency from "@/components/formatted-currency";
import { X } from "lucide-react";
import { Edit2 } from "../../../../../../../../../../public/images/icons";
import { formatDate } from "@/lib/dateFormatter";
import { TinputField, TFunction } from "@/types";
import { Locale } from "@/i18n/routing";
import { normalizeValue } from "../utils/salary-form-utils";
import { SalaryPackageSchemaType } from "@/app/[locale]/_modules/people/schemas/salaryPackageSchema";
import useMediaQuery from "@/hooks/use-media-query";
import { useTranslations } from "next-intl";
import {
  calculateDynamicWidth,
  generateWidthDots,
  getCurrencySymbol,
} from "@/utils/input-utils";

type SalaryFieldRowProps = {
  field: TinputField<SalaryPackageSchemaType>;
  index: number;
  totalFields: number;
  form: UseFormReturn<SalaryPackageSchemaType>;
  isPending: boolean;
  isEditing: boolean;
  onToggleEdit: (fieldName: string) => void;
  t: TFunction;
  locale: Locale;
};

export const SalaryFieldRow: React.FC<SalaryFieldRowProps> = ({
  field,
  index,
  totalFields,
  form,
  isPending,
  isEditing,
  onToggleEdit,
  t: _t,
  locale,
}) => {
  const isMobile = useMediaQuery("(max-width: 767px)");
  const translations = useTranslations();

  const currentVal = form.getValues(
    field.name as keyof SalaryPackageSchemaType,
  );
  const originalVal =
    form.formState.defaultValues?.[field.name as keyof SalaryPackageSchemaType];
  const isEdited = normalizeValue(currentVal) !== normalizeValue(originalVal);

  const currencySymbol = getCurrencySymbol(translations, isMobile);
  const currentValue = String(currentVal || "");
  const inputWidth = calculateDynamicWidth(currentValue);

  const generateDots = (
    width: string,
    dotConfig: {
      dotSize?: string;
      dotColor?: string;
      gapSize?: number;
    } = {},
  ) => generateWidthDots(width, dotConfig);

  const renderFieldValue = () => {
    if (field.type === "text") {
      return (
        <FormattedCurrency
          amount={parseFloat((currentVal as string)?.replace(/,/g, "") || "0")}
          wrapperStyle="justify-end w-fit"
          numberStyle={`text-base font-semibold ${isEdited ? "text-amber-600" : "text-gray-900"}`}
          currencyStyle="text-gray-900 text-xs text-medium"
        />
      );
    }

    if (field.type === "date") {
      return (
        <span
          className={`text-base font-semibold ${isEdited ? "text-amber-600" : "text-gray-900"}`}
        >
          {formatDate(currentVal as string, locale)}
        </span>
      );
    }

    return (
      <span
        className={`text-base font-semibold ${isEdited ? "text-amber-600" : "text-gray-900"}`}
      >
        {String(currentVal || "")}
      </span>
    );
  };

  const renderEditButton = (isMobile = false) => (
    <Button
      type="button"
      variant="ghost"
      onClick={() => onToggleEdit(field.name || "")}
      className={`h-6 w-6 p-0 hover:bg-gray-100 flex items-center ${isMobile ? "md:hidden" : "max-sm:hidden"}`}
    >
      {isEditing ? (
        <div className="w-5 h-5 flex items-center justify-center rounded-full border-2 border-black">
          <X className="!h-3 !w-3 scale-95" />
        </div>
      ) : (
        <div
          className={`border-b-2 border-black ${isMobile ? "" : "hidden md:group-hover:flex"}`}
        >
          <Edit2 className="!h-5 !w-6 scale-95" />
        </div>
      )}
    </Button>
  );

  return (
    <div
      className={`font-readex_pro pb-4 mb-4 ${
        index !== totalFields - 1 ? "border-b border-gray-100" : ""
      }`}
    >
      <div className="flex items-start justify-between group">
        <span className="text-base text-gray-500 flex-1 font-medium">
          {field.label}:
        </span>
        <div className="flex items-start gap-2">
          {/* Desktop edit button */}
          {renderEditButton(false)}
          {/* Mobile edit button */}
          {renderEditButton(true)}

          <div className="text-start">
            {isEditing ? (
              <div className="inline-block">
                {field.type === "text" ? (
                  <div className="flex items-center justify-end max-w-40 gap-1">
                    <div
                      className="relative"
                      style={{ width: inputWidth, maxWidth: "10rem" }}
                    >
                      <FormFieldRenderer
                        fieldConfig={{
                          ...field,
                          label: undefined,
                          labelClassName: undefined,
                          inputClassName: "w-fit",
                          className:
                            "text-start w-full h-8 font-semibold text-sm mt-0 border-none shadow-none p-0 focus-visible:ring-0 h-auto text-amber-600",
                        }}
                        textInputWrapper="!mt-0"
                        form={form}
                        isPending={isPending}
                      />
                      {/* Dynamic dots filling the input width during editing */}
                      <div className="absolute -bottom-1 end-0 start-0 flex justify-between">
                        {generateDots(inputWidth, { dotColor: "bg-amber-600" })}
                      </div>
                    </div>
                    <span className="text-xs font-medium text-gray-900 shrink-0">
                      {currencySymbol}
                    </span>
                  </div>
                ) : (
                  <div className="relative">
                    <FormFieldRenderer
                      fieldConfig={{
                        ...field,
                        label: undefined,
                        labelClassName: undefined,
                        className:
                          "text-start ps-0 h-8 !text-base font-semibold !mt-0.5 border-none shadow-none p-0 focus-visible:ring-0 h-auto text-amber-600 w-full",
                      }}
                      textInputWrapper="!mt-0"
                      form={form}
                      isPending={isPending}
                    />
                    {/* Dynamic dots filling the input width during editing - same as currency */}
                    <div className="absolute -bottom-1 end-0 start-0 flex justify-between w-full">
                      {generateDots(inputWidth)}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="relative">
                <span
                  className={`text-lg font-semibold ${
                    isEdited ? "text-amber-600" : ""
                  }`}
                >
                  {renderFieldValue()}
                </span>
                {/* Show dots under changed values in display mode */}
                {isEdited && (
                  <div
                    className={`absolute -bottom-1 flex justify-between max-w-40 ${
                      field.type === "text" ? "start-0" : "!left-0"
                    }`}
                    style={{
                      width:
                        field.type === "text"
                          ? calculateDynamicWidth(
                              currentValue.replace(/[^\d,]/g, ""),
                            )
                          : inputWidth,
                    }}
                  ></div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
