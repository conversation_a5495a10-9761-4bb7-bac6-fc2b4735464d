"use client";

import { cn } from "@/lib/utils";
import { Check } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

type CircularProgressProps = {
  value: number;
  max: number;
  size?: number;
  strokeWidth?: number;
  className?: string;
  showCheckmark?: boolean;
  showNumber?: boolean;
  variant?: "default" | "success" | "pending" | "destructive" | "avatar";
  avatarSrc?: string;
  avatarFallback?: string;
};

export function CircularProgress({
  value,
  max,
  size = 40,
  strokeWidth = 3,
  className,
  showCheckmark = false,
  showNumber = true,
  variant = "default",
  avatarSrc,
  avatarFallback,
}: CircularProgressProps) {
  const percentage = max > 0 ? (value / max) * 100 : 0;
  const radius = (size - strokeWidth) / 2;
  const circumference = radius * 2 * Math.PI;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  const getVariantStyles = () => {
    switch (variant) {
      case "success":
        return {
          circle: "text-secondary",
          background: "text-transparent",
          text: "text-secondary",
          backgroundFill: "#BBF49C",
        };
      case "pending":
        return {
          circle: "text-amber-500",
          background: "text-transparent",
          text: "text-amber-500",
          backgroundFill: "#FFFBEB",
        };
      case "avatar":
        return {
          circle: "text-orange-500",
          background: "text-transparent",
          text: "text-orange-600",
          backgroundFill: "bg-yellow-500",
        };
      default:
        return {
          circle: "text-orange-500",
          background: "text-transparent",
          text: "text-gray-600 text-sm font-bold",
          backgroundFill: "#f3f4f6",
        };
    }
  };

  const styles = getVariantStyles();

  if (variant === "avatar" && avatarSrc) {
    return (
      <div
        className={cn(
          "relative inline-flex items-center justify-center",
          className,
        )}
      >
        <Avatar className="w-10 h-10">
          <AvatarImage src={avatarSrc || "/placeholder.svg"} />
          <AvatarFallback className="text-sm">{avatarFallback}</AvatarFallback>
        </Avatar>
      </div>
    );
  }

  return (
    <div
      className={cn(
        "relative inline-flex items-center justify-center",
        className,
      )}
    >
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill={styles.backgroundFill}
          className={styles.background}
        />
        {/* Progress circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="currentColor"
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={cn(
            "transition-all duration-300 ease-in-out",
            styles.circle,
          )}
        />
      </svg>
      {/* Content */}
      <div className="absolute inset-0 flex items-center justify-center">
        {showCheckmark && percentage === 100 ? (
          <Check className={cn("w-5 h-5 stroke-[3px]", styles.text)} />
        ) : showNumber ? (
          <span className={cn("text-sm font-semibold", styles.text)}>
            {value}
          </span>
        ) : null}
      </div>
    </div>
  );
}
