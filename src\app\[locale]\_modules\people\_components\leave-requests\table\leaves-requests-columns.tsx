"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { REQUEST_STATUS } from "@/constants/enum";
import {
  getMainCategoryTranslation,
  mapRequestStatusToCanonical,
} from "@/constants/translations-mapping";
import { LeavesRequestsActions } from "./leaves-requests-actions";
import RequestStatus from "@/components/status/request-status";
import { TMainCategory } from "../../../type/leave-request";
import { Locale } from "@/i18n/routing";
import { formatDate } from "@/lib/dateFormatter";
import { LeaveDetail, TIncludedEmployee } from "../../../type/employee-leaves";
import { TFunction } from "@/types";
import { TEmployee } from "../../../type/employee";
import { findEmployeeById } from "../../../utils/find-employee";
import ApprovalWorkFlow from "../../approval-workflow";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<LeaveDetail> {
  t: TFunction;
  locale?: Locale;
  onShowDetails: (leaveRequest: LeaveDetail, employee: TEmployee) => void;
  // onShowNote: (leaveRequest: LeaveDetail, employee: TEmployee) => void;
  onAcceptRequest: (leaveRequest: LeaveDetail, employee: TEmployee) => void;
  onRejectRequest: (leaveRequest: LeaveDetail, employee: TEmployee) => void;
  employeeData: TIncludedEmployee[];
}

// Helper function to get meta data
const getMeta = (table: Table<LeaveDetail>) =>
  table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<LeaveDetail>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },

  // employee Name
  {
    accessorKey: "employee_name",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.leaves-requests-page.table.columns.employee_name",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { employeeData } = getMeta(table);
      const employeeId = row.original.relationships.employee.data.id;
      const employee = findEmployeeById(employeeData, employeeId);
      return (
        employee && (
          <p className="text-sm font-semibold text-start text-gray-500">
            {employee.name}
          </p>
        )
      );
    },
    meta: { filterVariant: "select" },
  },

  // request type
  {
    accessorKey: "leave_type",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.leaves-requests-page.table.columns.leave_type",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {getMainCategoryTranslation(row.original.type as TMainCategory, t)}
        </p>
      );
    },
  },

  // leave request start date
  {
    accessorKey: "start_date",
    enableColumnFilter: true,
    meta: {
      filterType: "date",
    },
    header: ({ table }) => {
      return (
        <div>
          {getMeta(table).t(
            "people.leaves-requests-page.table.columns.start_date",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const dateStr = row.original.attributes.start_date;
      const formateDate = dateStr && formatDate(dateStr, locale ?? "en");

      return (
        <p className="text-sm font-semibold text-gray-500">{formateDate}</p>
      );
    },
  },

  // Status Column with Sorting

  {
    accessorKey: "status",
    header: ({ table }) =>
      getMeta(table).t("people.leaves-requests-page.table.columns.status"),
    cell: ({ row, table }) => {
      const { employeeData, onShowDetails } = getMeta(table);
      return (
        <div className="flex justify-start">
          <ApprovalWorkFlow
            item={row.original}
            included={employeeData}
            onShowDetails={onShowDetails}
          />
        </div>
      );
    },
  },

  // Actions Column
  {
    accessorKey: "actions",
    id: "actions",
    enableHiding: false,
    header: ({ table }) => getMeta(table).t("cm.table.columns.actions"),
    cell: ({ row, table }) => {
      const {
        onShowDetails,
        // onShowNote,
        onAcceptRequest,
        employeeData,
        onRejectRequest,
      } = getMeta(table);
      return (
        <div className="mx-auto flex justify-center">
          <LeavesRequestsActions<LeaveDetail>
            row={row}
            onAccept={onAcceptRequest}
            onReject={onRejectRequest}
            // onShowNote={onShowNote}
            employeeData={employeeData}
            onShowDetails={onShowDetails}
          />
        </div>
      );
    },
  },
];
