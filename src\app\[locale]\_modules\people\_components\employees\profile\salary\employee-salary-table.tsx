"use client";

import { DataTable } from "@/components/table";
import React, { useState } from "react";
import { useLocale, useTranslations } from "next-intl";
import { PaginationWithLinks } from "@/components/pagination-with-links";
import { RowSelectionState } from "@tanstack/react-table";
import { useSalaryMutations } from "../../../../hooks/useSalaryMutations";
import { employeeSalaryColumns } from "../../../shared/salary-columns";
import { useSearchParams } from "next/navigation";

type EmployeeSalaryTableProps = {
  employeeId: string;
};

export const EmployeeSalaryTable = ({
  employeeId,
}: EmployeeSalaryTableProps) => {
  const t = useTranslations();
  const locale = useLocale();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? "5";
  const page = searchParams.get("page") ?? "1";
  const [rowSelection, setRowSelection] = useState<RowSelectionState>({});

  const {
    SalaryStatus: salaryCalculations,
    pagination,
    isLoading,
    error,
    submitSalary,
    approveSalary,
    rejectSalary,
    isSubmitting,
    isApproving,
    isRejecting,
  } = useSalaryMutations({
    employeeId,
    page: Number(page),
    limit: Number(limit),
  });

  // Handle submit salary (change from draft to submitted)
  const handleSubmitSalary = (salaryId: string) => {
    submitSalary(salaryId);
  };

  // Handle approve salary
  const handleApproveSalary = (salaryId: string) => {
    approveSalary(salaryId);
  };

  // Handle reject salary
  const handleRejectSalary = (salaryId: string) => {
    rejectSalary(salaryId);
  };

  return (
    <>
      <DataTable
        data={salaryCalculations}
        dataCount={pagination?.count}
        columns={employeeSalaryColumns}
        title={t("people.employees-page.profile.salary.table.title")}
        meta={{
          t,
          locale: locale,
          onSubmitSalary: handleSubmitSalary,
          onApproveSalary: handleApproveSalary,
          onRejectSalary: handleRejectSalary,
          isSubmitting,
          isApproving,
          isRejecting,
        }}
        rowSelection={rowSelection}
        onRowSelectionChange={setRowSelection}
        translationPrefix="people.employees-page.profile.salary.table"
        tableId="employee-profile-salary"
        isLoading={isLoading}
        initialLimit={5}
        error={error}
        hideSearch={true}
        hideFilters={true}
      />

      {/* Pagination */}
      <div className="w-full pt-[18px]">
        <PaginationWithLinks
          page={Number(page)}
          pageSize={Number(limit)}
          totalCount={pagination?.count || 0}
          firstLastCounts={{
            firstCount: pagination?.from || 1,
            lastCount: pagination?.to || 5,
          }}
          isLoading={isLoading}
          isDisabled={!salaryCalculations?.length}
          pageSizeSelectOptions={{
            pageSizeOptions: [5, 10, 25, 30, 45, 50],
            pageSizeSearchParam: "limit",
          }}
        />
      </div>
    </>
  );
};
