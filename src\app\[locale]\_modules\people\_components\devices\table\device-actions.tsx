"use client";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { TFunction } from "@/types";
import { useLocale, useTranslations } from "next-intl";
import { useState } from "react";
import { Edit, Trash2, MoreVertical } from "lucide-react";
import { TDevice } from "../../../type/devices/device";
import { LANGUAGES } from "@/constants/enum";
import DeviceActionsDialogs from "../shared/device-actions-dialogs";

type DeviceActionsProps = {
  device: TDevice;
};

const DeviceActions = ({ device }: DeviceActionsProps) => {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const isAr = locale === LANGUAGES.ARABIC;

  const [isOpen, setIsOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const actionsState = {
    isEditDialogOpen,
    isDeleteDialogOpen,
    selectedDevice: device || null,
  };

  const actionsHandlers = {
    handleView: (_device: TDevice) => {
      // Navigation handled elsewhere if needed
    },
    handleEdit: (_device: TDevice) => {
      setIsOpen(false);
      setIsEditDialogOpen(true);
    },
    handleDelete: (_device: TDevice) => {
      setIsOpen(false);
      setIsDeleteDialogOpen(true);
    },

    closeEditDialog: () => {
      setIsEditDialogOpen(false);
    },
    closeDeleteDialog: () => {
      setIsDeleteDialogOpen(false);
    },
  };

  return (
    <>
      <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="!p-2 border hover:border-black/50 rounded-lg group transition-colors"
          >
            <span className="sr-only">Open menu</span>
            <MoreVertical className="w-4 text-gray-500 group-hover:text-black" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align={isAr ? "start" : "end"}>
          <DropdownMenuItem onClick={() => actionsHandlers.handleEdit(device)}>
            <Edit className="mr-2 h-4 w-4" />
            {t("people.devices-page.table.actions.edit")}
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => actionsHandlers.handleDelete(device)}
            className="text-destructive"
          >
            <Trash2 className="mr-2 h-4 w-4" />
            {t("people.devices-page.table.actions.delete")}
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <DeviceActionsDialogs state={actionsState} handlers={actionsHandlers} />
    </>
  );
};
export default DeviceActions;
