import { Suspense } from "react";
import AttendanceRequestsTable from "../../_modules/people/_components/attendance-requests/table";

const Requests = async ({
  searchParams,
}: {
  searchParams: Promise<{ page: string; limit: string }>;
}) => {
  const params = await searchParams;
  return (
    <>
      <RequestsHeader title="هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة" />
      <Suspense fallback={<div>Loading...</div>}>
        <AttendanceRequestsTable showPagination={true} searchParams={params} />
      </Suspense>
    </>
  );
};

export default Requests;

const RequestsHeader = ({ title }: { title: string }) => {
  return (
    <h2 className="max-w-[301px] leading-[120%] text-base text-secondary mb-0.5">
      {title}
    </h2>
  );
};
