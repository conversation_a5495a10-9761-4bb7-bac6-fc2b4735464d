"use client";

import { Row } from "@tanstack/react-table";
import { useLocale, useTranslations } from "next-intl";
import { Locale } from "@/i18n/routing";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import { SalaryCalculation } from "../../../../type/employees-salaries";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Eye, Download } from "lucide-react";
import { LANGUAGES, SALARY_STATUS } from "@/constants/enum";
import { PermissionEnum } from "@/enums/Permission";
import { usePermission } from "@/contexts/PermissionContext";
import { downloadFile } from "@/lib/downloadFile";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useState } from "react";
import FilePreview from "@/components/file-preview";

type EmployeeSalaryActionsProps<TData> = {
  row: Row<TData>;
  onApproveSalary?: (salaryId: string) => void;
  onRejectSalary?: (salaryId: string) => void;
  onSubmitSalary?: (salaryId: string) => void;
  isApproving?: boolean;
  isRejecting?: boolean;
  isSubmitting?: boolean;
};

export const EmployeeSalaryActions = <TData extends SalaryCalculation>({
  row,
  onApproveSalary,
  onRejectSalary,
  onSubmitSalary,
  isApproving,
  isRejecting,
  isSubmitting,
}: EmployeeSalaryActionsProps<TData>) => {
  const locale: Locale = useLocale() as Locale;
  const isAr = locale === LANGUAGES.ARABIC;
  const t = useTranslations();
  const status = row.original.attributes.status;
  const canonicalStatus = mapSalaryStatusToCanonical(status);
  const { hasPermission } = usePermission();
  const { showToast } = useToastMessage();
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [previewData, setPreviewData] = useState<{
    fileName: string;
    fileUrl: string;
    fileType: string;
  } | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);

  // Check if the status is draft (only show submit option)
  const isDraft = canonicalStatus === SALARY_STATUS.DRAFT;

  // For salaries, only SUBMITTED status can be approved/rejected
  const canBeApprovedOrRejected = canonicalStatus === SALARY_STATUS.SUBMITTED;

  // Check if salary slip is available
  const hasSlip = row.original.attributes.has_salary_slip;

  // Check permissions
  const canSubmitSalary = hasPermission(
    PermissionEnum.SUBMIT_SALARY_CALCULATION,
  );
  const canApproveSalary = hasPermission(
    PermissionEnum.APPROVE_SALARY_CALCULATION,
  );

  // Check if actions are available based on permissions and status
  const hasSubmitAction = isDraft && onSubmitSalary && canSubmitSalary;
  const hasApproveAction =
    canBeApprovedOrRejected && onApproveSalary && canApproveSalary;
  const hasRejectAction =
    canBeApprovedOrRejected && onRejectSalary && canApproveSalary;

  // Slip actions are available if slip exists
  const hasSlipActions = hasSlip;

  // Check if there are any actions available
  const hasActions = hasSubmitAction || hasApproveAction || hasRejectAction || hasSlipActions;

  // Handle slip preview
  const handlePreviewSlip = async () => {
    try {
      const response = await fetch(
        `/api/finance/salary_calculations/${row.original.id}/slip?action=preview`
      );

      if (!response.ok) {
        throw new Error("Failed to get slip preview");
      }

      const data = await response.json();

      setPreviewData({
        fileName: data.fileName || `salary-slip-${row.original.id}.pdf`,
        fileUrl: data.fileUrl,
        fileType: data.fileType || "application/pdf",
      });
      setIsPreviewOpen(true);
    } catch (error) {
      showToast(
        "error",
        t("people.employees-page.profile.salary.slip.preview-error")
      );
    }
  };

  // Handle slip download
  const handleDownloadSlip = async () => {
    if (isDownloading) return;

    setIsDownloading(true);
    try {
      const response = await fetch(
        `/api/finance/salary_calculations/${row.original.id}/slip?action=download`
      );

      if (!response.ok) {
        throw new Error("Failed to download slip");
      }

      const blob = await response.blob();
      const fileName = `salary-slip-${row.original.id}.pdf`;
      const url = URL.createObjectURL(blob);

      downloadFile(url, fileName);
      URL.revokeObjectURL(url);

      showToast(
        "success",
        t("people.employees-page.profile.salary.slip.download-success")
      );
    } catch (error) {
      showToast(
        "error",
        t("people.employees-page.profile.salary.slip.download-error")
      );
    } finally {
      setIsDownloading(false);
    }
  };

  return (
    <>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className="h-8 w-8 p-0 focus-visible:ring-0 focus-visible:ring-offset-0"
          disabled={isApproving || isRejecting || !hasActions}
        >
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align={isAr ? "start" : "end"} className="w-[180px]">
        {/* Slip actions */}
        {hasSlipActions && (
          <>
            <DropdownMenuItem
              onClick={handlePreviewSlip}
              className="cursor-pointer font-medium text-blue-600 rtl:justify-end"
            >
              <Eye className="h-4 w-4 mr-2" />
              {t("people.employees-page.profile.salary.slip.preview") || "Preview Slip"}
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={handleDownloadSlip}
              disabled={isDownloading}
              className="cursor-pointer font-medium text-green-600 rtl:justify-end"
            >
              <Download className="h-4 w-4 mr-2" />
              {isDownloading
                ? t("people.employees-page.profile.salary.slip.downloading") || "Downloading..."
                : t("people.employees-page.profile.salary.slip.download") || "Download Slip"
              }
            </DropdownMenuItem>
            {(hasSubmitAction || hasApproveAction || hasRejectAction) && (
              <DropdownMenuSeparator />
            )}
          </>
        )}

        {/* Submit option for draft status */}
        {hasSubmitAction && (
          <DropdownMenuItem
            onClick={() => onSubmitSalary!(row.original.id)}
            disabled={isSubmitting}
            className="cursor-pointer font-medium text-blue-600 rtl:justify-end"
          >
            {t("people.employees-salaries-page.table.actions.submit") ||
              "Submit"}
          </DropdownMenuItem>
        )}

        {/* Approve option for submitted status */}
        {hasApproveAction && (
          <DropdownMenuItem
            onClick={() => onApproveSalary!(row.original.id)}
            disabled={isApproving}
            className="cursor-pointer font-medium text-green-600 rtl:justify-end"
          >
            {t("people.employees-page.profile.salary.table.actions.approve")}
          </DropdownMenuItem>
        )}

        {/* Reject option for submitted status */}
        {hasRejectAction && (
          <DropdownMenuItem
            onClick={() => onRejectSalary!(row.original.id)}
            disabled={isRejecting}
            className="cursor-pointer font-medium text-red-600 rtl:justify-end"
          >
            {t("people.employees-page.profile.salary.table.actions.reject")}
          </DropdownMenuItem>
        )}

        {/* No actions available message */}
        {!hasActions && (
          <DropdownMenuItem
            className="cursor-default text-gray-400 rtl:justify-end"
            disabled
          >
            <span className="text-sm font-semibold text-end">
              {t("common.NoActionsAvailable")}
            </span>
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>

    {/* File Preview Modal */}
    {previewData && (
      <FilePreview
        fileName={previewData.fileName}
        fileUrl={previewData.fileUrl}
        fileType={previewData.fileType}
        isOpen={isPreviewOpen}
        onClose={() => {
          setIsPreviewOpen(false);
          setPreviewData(null);
        }}
      />
    )}
  </>
  );
};
