"use client";

import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import {
  acceptRequest,
  rejectRequest as rejectRequestAction,
} from "../actions/requests";
import { withdrawLeaveRequest as withdrawLeaveRequestAction } from "../actions/leave-requests";
import {
  ApprovalActionResponse,
  ApprovalRequestData,
} from "../type/approval-request";
import { useTranslations } from "next-intl";

/**
 * A unified hook for handling approval requests (leaves, salaries, etc.)
 * This can be used across different components that need to approve/reject requests
 */
export const useApprovalMutations = (
  type: "leave" | "salary" = "leave", // Default to leave for backward compatibility
) => {
  const [isApproving, setIsApproving] = useState(false);
  const [isRejecting, setIsRejecting] = useState(false);
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const { showToast } = useToastMessage();
  const t = useTranslations();

  // Get the appropriate success and error messages based on the type
  const getMessages = () => {
    if (type === "salary") {
      return {
        approveSuccess: t(
          "people.employees-page.profile.salary.table.actions.approve-success",
        ),
        approveError: t(
          "people.employees-page.profile.salary.table.actions.approve-error",
        ),
        rejectSuccess: t(
          "people.employees-page.profile.salary.table.actions.reject-success",
        ),
        rejectError: t(
          "people.employees-page.profile.salary.table.actions.reject-error",
        ),
      };
    }

    // Default to leave messages
    return {
      approveSuccess: t("people.leaves-requests-page.actions.approve-success"),
      approveError: t("people.leaves-requests-page.actions.approve-error"),
      rejectSuccess: t("people.leaves-requests-page.actions.reject-success"),
      rejectError: t("people.leaves-requests-page.actions.reject-error"),
    };
  };

  const approveRequest = async (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ) => {
    try {
      setIsApproving(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await acceptRequest(approvalRequestId, comment);

      if (result.error) {
        throw result.error;
      }

      // Check if this is the final approval or an intermediate step
      const isFinalApproval =
        result.data?.approvalRequest?.attributes?.current_step === null;

      const messages = getMessages();
      showToast(
        "success",
        isFinalApproval && type === "leave"
          ? t("people.leaves-requests-page.actions.approve-final-success")
          : messages.approveSuccess,
      );

      // Call success callback if provided, passing the approval request data
      if (onSuccess) {
        onSuccess(result.data?.approvalRequest);
      }

      return result;
    } catch (error) {
      const messages = getMessages();
      showToast(
        "error",
        error instanceof Error ? error.message : messages.approveError,
      );
      throw error;
    } finally {
      setIsApproving(false);
    }
  };

  const rejectRequest = async (
    approvalRequestId: string,
    comment: string = "",
    onSuccess?: (approvalRequest?: ApprovalRequestData) => void,
    onOptimisticUpdate?: () => void,
  ): Promise<ApprovalActionResponse> => {
    try {
      setIsRejecting(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await rejectRequestAction(approvalRequestId, comment);

      if (result.error) {
        throw result.error;
      }

      const messages = getMessages();
      showToast("success", messages.rejectSuccess);

      // Call success callback if provided, passing the approval request data
      if (onSuccess) {
        onSuccess(result.data?.approvalRequest);
      }

      return result;
    } catch (error) {
      const messages = getMessages();
      showToast(
        "error",
        error instanceof Error ? error.message : messages.rejectError,
      );
      throw error;
    } finally {
      setIsRejecting(false);
    }
  };

  const withdrawLeaveRequest = async (
    leaveId: string,
    employeeId: string,
    onSuccess?: () => void,
    onOptimisticUpdate?: () => void,
  ): Promise<ApprovalActionResponse> => {
    try {
      setIsWithdrawing(true);

      // Perform optimistic update if provided
      if (onOptimisticUpdate) {
        onOptimisticUpdate();
      }

      const result = await withdrawLeaveRequestAction(leaveId, employeeId);

      if (result.error) {
        throw result.error;
      }

      showToast("success", "Leave request withdrawn successfully");

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

      return result;
    } catch (error) {
      showToast(
        "error",
        error instanceof Error
          ? error.message
          : "Failed to withdraw leave request",
      );
      throw error;
    } finally {
      setIsWithdrawing(false);
    }
  };

  return {
    approveRequest,
    rejectRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing,
  };
};
