"use client";

import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";
import { ApprovalRequestData } from "../type/approval-request";
import { 
  hasUserAlreadyActed, 
  isUserApprover, 
  getApprovalStatus,
  getUserActionInfo 
} from "../utils/approval-utils";

/**
 * Custom hook for handling approval permissions and status
 * This hook provides all the necessary information about a user's ability to act on approval requests
 * 
 * @param approvalRequest - The approval request data
 * @returns Object containing permission flags and status information
 */
export const useApprovalPermissions = (
  approvalRequest: ApprovalRequestData | undefined,
) => {
  const { profile: currentEmployee } = useCurrentEmployee();
  const currentUserId = currentEmployee?.attributes?.user_id;

  // Get user action status
  const { hasActed, action: userAction, canAct } = hasUserAlreadyActed(
    approvalRequest,
    currentUserId || 0,
  );

  // Check if user is an approver
  const isApprover = isUserApprover(approvalRequest, currentUserId || 0);

  // Get approval status
  const approvalStatus = getApprovalStatus(approvalRequest);

  // Get detailed user action info
  const userActionInfo = getUserActionInfo(approvalRequest, currentUserId || 0);

  // Determine if buttons should be shown
  const shouldShowButtons = canAct && approvalStatus.status === "pending";

  // Get status message for UI feedback
  const getStatusMessage = (t: (key: string) => string) => {
    if (approvalStatus.status !== "pending") return null;
    
    if (hasActed) {
      return userAction === "approve" 
        ? t("people.leaves-requests-component.actions.alreadyApproved")
        : t("people.leaves-requests-component.actions.alreadyRejected");
    }
    
    if (!isApprover) {
      return t("people.leaves-requests-component.actions.notAuthorized");
    }
    
    return null;
  };

  return {
    // Permission flags
    canAct,
    hasActed,
    isApprover,
    shouldShowButtons,
    
    // User action details
    userAction,
    userActionInfo,
    
    // Approval status
    approvalStatus,
    
    // UI helpers
    getStatusMessage,
    
    // Current user info
    currentUserId,
    currentEmployee,
  };
};
