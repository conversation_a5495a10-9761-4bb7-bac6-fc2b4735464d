"use client";

import { useState } from "react";
import NotificationsList from "@/components/navbar/notification/notification-list";
import { Notification } from "@/types";

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: 1,
      title: "اقترب موعد جلستك",
      description:
        "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة، لقد تم توليد هذا النص من مولد النص العربى",
      date: "05:00 | 25-01-2025",
      isRead: false,
    },
    {
      id: 2,
      title: "تم الغاء موعد جلستك",
      description: "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 3,
      title: "تم استلام التقرير من الطبيب",
      description: "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 4,
      title: "تم استلام التقرير من الطبيب",
      description: "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
    {
      id: 5,
      title: "تم استلام التقرير من الطبيب",
      description: "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة",
      date: "05:00 | 25-01-2025",
      isRead: true,
    },
  ]);

  const markAllAsRead = () => {
    setNotifications((prev) =>
      prev.map((notification) => ({ ...notification, isRead: true })),
    );
  };

  const markAsRead = (id: number) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, isRead: true } : n)),
    );
  };

  return (
    <div className="w-full">
      <NotificationsList
        containerStyle="h-dvh"
        notifications={notifications}
        markAsRead={markAsRead}
        markAllAsRead={markAllAsRead}
      />
    </div>
  );
}
