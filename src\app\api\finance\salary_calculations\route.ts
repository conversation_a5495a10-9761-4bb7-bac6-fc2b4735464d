import { NextRequest, NextResponse } from "next/server";
import { peopleService } from "@/services/api/people";
import { createErrorResponse } from "@/utils/api-error-extractor";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);

    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);

    // Extract employee ID from the filter parameter
    const employeeId = searchParams.get("filter[employee_id_eq]") || "";

    try {
      const response = await peopleService.getSalaryCalculations(
        page,
        limit,
        employeeId,
      );

      // Return the response directly without wrapping it
      return NextResponse.json(response);
    } catch (error) {
      // Re-throw for the outer catch block to handle
      throw error;
    }
  } catch (error) {
    const { error: errorMessage, status } = createErrorResponse(
      error,
      "GET salary calculations route",
    );
    return NextResponse.json({ error: errorMessage }, { status });
  }
}
