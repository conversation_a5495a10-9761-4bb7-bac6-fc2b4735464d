import { ApprovalRequestData } from "../type/approval-request";

/**
 * Utility functions for handling approval workflow logic
 */

/**
 * Checks if the current user has already acted on the current approval step
 * 
 * @param approvalRequest - The approval request data
 * @param currentUserId - The current user's ID (from employee.attributes.user_id)
 * @returns Object containing hasActed, action, and canAct flags
 */
export const hasUserAlreadyActed = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): { hasActed: boolean; action?: "approve" | "reject"; canAct: boolean } => {
  // If no approval request or no current user, can't act
  if (!approvalRequest || !currentUserId) {
    return { hasActed: false, canAct: false };
  }

  const { current_step, steps, status } = approvalRequest.attributes;

  // If request is already completed (no current step), no one can act
  if (!current_step || status === "approved" || status === "rejected") {
    return { hasActed: false, canAct: false };
  }

  // Find the matching step (by ID) in the steps array
  const step = steps.find((s) => s.id === current_step.id);
  if (!step) {
    return { hasActed: false, canAct: false };
  }

  // Check if current user is an approver for this step
  // Ensure approver_ids exists before calling includes
  const isApprover =
    current_step.approver_ids?.includes(currentUserId.toString()) ?? false;
  if (!isApprover) {
    return { hasActed: false, canAct: false };
  }

  // Check if user has already acted on this step
  const existingAction = step.actions.find(
    (a) => a.user_id === currentUserId.toString(),
  );

  return {
    hasActed: !!existingAction,
    action: existingAction?.action,
    canAct: !existingAction, // Can act only if user hasn't acted yet
  };
};

/**
 * Checks if the current user is an approver for the current step
 * 
 * @param approvalRequest - The approval request data
 * @param currentUserId - The current user's ID
 * @returns boolean indicating if user is an approver
 */
export const isUserApprover = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): boolean => {
  if (!approvalRequest || !currentUserId) {
    return false;
  }

  const { current_step } = approvalRequest.attributes;
  if (!current_step) {
    return false;
  }

  return current_step.approver_ids?.includes(currentUserId.toString()) ?? false;
};

/**
 * Gets the approval status for display purposes
 * 
 * @param approvalRequest - The approval request data
 * @returns Object with status information
 */
export const getApprovalStatus = (
  approvalRequest: ApprovalRequestData | undefined,
): {
  status: "pending" | "approved" | "rejected" | "completed";
  currentStepName?: string;
  isCompleted: boolean;
} => {
  if (!approvalRequest) {
    return { status: "pending", isCompleted: false };
  }

  const { current_step, status } = approvalRequest.attributes;

  // If no current step, the request is completed
  if (!current_step) {
    return {
      status: status === "approved" ? "approved" : "rejected",
      isCompleted: true,
    };
  }

  // If current step is rejected
  if (current_step.rejected) {
    return {
      status: "rejected",
      currentStepName: current_step.name,
      isCompleted: true,
    };
  }

  // If current step is complete
  if (current_step.complete) {
    return {
      status: "approved",
      currentStepName: current_step.name,
      isCompleted: false, // May have more steps
    };
  }

  // Step is pending
  return {
    status: "pending",
    currentStepName: current_step.name,
    isCompleted: false,
  };
};

/**
 * Gets user action information for the current step
 * 
 * @param approvalRequest - The approval request data
 * @param currentUserId - The current user's ID
 * @returns Object with user's action details
 */
export const getUserActionInfo = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): {
  hasActed: boolean;
  action?: "approve" | "reject";
  actionDate?: string;
  comment?: string;
} => {
  const { hasActed, action } = hasUserAlreadyActed(approvalRequest, currentUserId);
  
  if (!hasActed || !approvalRequest) {
    return { hasActed: false };
  }

  const { current_step, steps } = approvalRequest.attributes;
  if (!current_step) {
    return { hasActed: false };
  }

  const step = steps.find((s) => s.id === current_step.id);
  const userAction = step?.actions.find(
    (a) => a.user_id === currentUserId.toString(),
  );

  return {
    hasActed: true,
    action: userAction?.action,
    actionDate: userAction?.created_at,
    comment: userAction?.comment,
  };
};
