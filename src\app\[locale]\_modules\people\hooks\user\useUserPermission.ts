import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { Permission } from "@/types/auth";
import { TSystems } from "@/types";

interface PermissionsResponse {
  data: Permission[];
  meta: {
    scope: string;
    count: number;
  };
}

export const useUserPermission = (system?: TSystems) => {
  // Only fetch permissions if a valid system is provided and it's not 'core'
  const shouldFetch = system && system !== "core";

  const { data, error, isLoading, mutate } = useSWR<PermissionsResponse>(
    shouldFetch ? `/api/users/me/permissions?scope=${system}` : null,
    fetcher,
  );

  return {
    permission: data?.data,
    isLoading,
    error,
    mutate,
  };
};
