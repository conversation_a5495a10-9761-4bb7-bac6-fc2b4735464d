import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { EmployeeLeavesResponse } from "../../type/employee-leaves";
import { updateLeaveDates as updateLeaveDatesAction } from "../../actions/leave-requests";
import { useState } from "react";
import { useToastMessage } from "@/hooks/use-toast-message";
import { useLeaveApprovalMutations } from "../useLeaveApprovalMutations";
import { ApprovalStep, ApprovalRequestData } from "../../type/approval-request";

type UseEmployeeLeaveMutationsProps = {
  employeeId: string;
  page: string;
  limit: string;
};

export const useEmployeeLeaveMutations = ({
  employeeId,
  page,
  limit,
}: UseEmployeeLeaveMutationsProps) => {
  const [isWithdrawing, setIsWithdrawing] = useState(false);
  const [isUpdatingDates, setIsUpdatingDates] = useState(false);
  const { showToast } = useToastMessage();

  // Use the unified approval mutations hook
  const {
    approveLeaveRequest,
    rejectLeaveRequest,
    withdrawLeaveRequest,
    isApproving,
    isRejecting,
    isWithdrawing: isWithdrawingFromHook,
  } = useLeaveApprovalMutations();

  const apiKey = employeeId
    ? `/api/leaves?page=${page}&limit=${limit}&include=employee,approval_request&sort=-id&filter[employee_id_eq]=${employeeId}`
    : "";

  type MutationParams =
    | string
    | {
        leaveId: string;
        startDate?: string;
        endDate?: string;
        leaveDuration?: string;
        leaveType?: string;
        note?: string;
        approvalRequestId?: string;
        comment?: string;
      };

  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    EmployeeLeavesResponse,
    MutationParams
  >({
    key: apiKey,
    fetcher: async () => {
      const { fetcher } = await import("@/services/fetcher");
      return fetcher(apiKey as string);
    },
    mutations: {
      withdrawLeave: {
        updateFn: (data, id) =>
          data
            ? {
                ...data,
                data: data.data.map((leave) =>
                  leave.id === id
                    ? {
                        ...leave,
                        attributes: {
                          ...leave.attributes,
                          status: "withdrawn",
                        },
                      }
                    : leave,
                ),
              }
            : data,
        mutationFn: async (leaveId) => {
          if (typeof leaveId !== "string") {
            throw new Error("Invalid leave ID for withdraw operation");
          }

          try {
            setIsWithdrawing(true);

            // Use the unified withdraw function
            await withdrawLeaveRequest(
              leaveId,
              employeeId,
              // No need for success callback as toast is handled in the unified function
              undefined,
              // No optimistic update callback as it's handled by the updateFn
              undefined,
            );

            return { data: undefined };
          } catch (error) {
            // Error handling and toast is already done in the unified function
            throw error;
          } finally {
            setIsWithdrawing(false);
          }
        },
      },
      updateDates: {
        updateFn: (data, params) => {
          if (
            typeof params === "string" ||
            !params.leaveId ||
            !params.startDate ||
            !params.endDate
          ) {
            return data;
          }

          const startDate = params.startDate || "";
          const endDate = params.endDate || "";

          return data
            ? {
                ...data,
                data: data.data.map((leave) => {
                  return leave.id === params.leaveId
                    ? {
                        ...leave,
                        attributes: {
                          ...leave.attributes,
                          start_date: startDate,
                          end_date: endDate,
                          leave_duration:
                            params.leaveDuration ||
                            leave.attributes.leave_duration,
                        },
                      }
                    : leave;
                }),
              }
            : data;
        },
        mutationFn: async (params) => {
          if (
            typeof params === "string" ||
            !params.leaveId ||
            !params.startDate ||
            !params.endDate
          ) {
            throw new Error("Invalid parameters for updating dates");
          }

          try {
            setIsUpdatingDates(true);

            const startDate = params.startDate || "";
            const endDate = params.endDate || "";

            const result = await updateLeaveDatesAction(
              params.leaveId,
              employeeId,
              startDate,
              endDate,
              params.leaveDuration,
              params.leaveType,
            );

            if (result && result.error) {
              throw result.error;
            }

            showToast("success", "Leave dates updated successfully");

            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              error instanceof Error
                ? error.message
                : "Failed to update leave dates",
            );
            throw error;
          } finally {
            setIsUpdatingDates(false);
          }
        },
      },
      acceptLeave: {
        updateFn: (data, params) => {
          if (typeof params === "string" || !params.leaveId) {
            return data;
          }

          if (!data) return data;

          const targetLeave = data.data.find(
            (leave) => leave.id === params.leaveId,
          );
          const approvalRequestId =
            targetLeave?.relationships?.approval_request?.data?.id;

          // First, determine the workflow completion status
          let isWorkflowComplete = false;
          let newLeaveStatus = targetLeave?.attributes.status || "pending";

          const approvalRequest = data.included?.find(
            (item) =>
              item.type === "approval_request" && item.id === approvalRequestId,
          ) as ApprovalRequestData | undefined;

          if (approvalRequest) {
            const currentStep = approvalRequest.attributes.current_step;
            const steps = approvalRequest.attributes.steps || [];
            const currentStepIndex = steps.findIndex(
              (step: ApprovalStep) => step.id === currentStep?.id,
            );
            const nextStepIndex = currentStepIndex + 1;
            isWorkflowComplete = nextStepIndex >= steps.length;
            newLeaveStatus = isWorkflowComplete ? "approved" : "pending";
          }

          return {
            ...data,
            data: data.data.map((leave) =>
              leave.id === params.leaveId
                ? {
                    ...leave,
                    attributes: {
                      ...leave.attributes,
                      status: newLeaveStatus,
                    },
                  }
                : leave,
            ),
            included:
              data.included?.map((item) => {
                if (
                  item.type === "approval_request" &&
                  item.id === approvalRequestId
                ) {
                  const approvalItem = item as ApprovalRequestData;
                  const currentStep = approvalItem.attributes.current_step;
                  const steps = approvalItem.attributes.steps || [];

                  // Find current step in the steps array
                  const currentStepIndex = steps.findIndex(
                    (step: ApprovalStep) => step.id === currentStep?.id,
                  );

                  // Create new action for the current step
                  const newAction = {
                    id: `temp-approve-${params.leaveId}`, // Stable temporary ID for optimistic update
                    user_id: "current_user", // This will be replaced by server response
                    action: "approve" as const,
                    comment:
                      typeof params === "string" ? "" : params.comment || "",
                    created_at: "pending", // Will be replaced by server response
                  };

                  // Update the steps array
                  const updatedSteps = steps.map((step: ApprovalStep) => {
                    if (step.id === currentStep?.id) {
                      return {
                        ...step,
                        complete: true,
                        rejected: false,
                        actions: [...(step.actions || []), newAction],
                      };
                    }
                    return step;
                  });

                  // Determine next step or completion
                  const nextStepIndex = currentStepIndex + 1;
                  const nextStep =
                    nextStepIndex < steps.length ? steps[nextStepIndex] : null;
                  const workflowComplete = nextStep === null;

                  return {
                    ...approvalItem,
                    attributes: {
                      ...approvalItem.attributes,
                      status: workflowComplete ? "approved" : "pending",
                      current_step: nextStep,
                      steps: updatedSteps,
                    },
                  };
                }
                return item;
              }) || [],
          } as EmployeeLeavesResponse;
        },
        mutationFn: async (params) => {
          if (typeof params === "string" || !params.leaveId) {
            throw new Error("Invalid parameters for accepting leave");
          }

          try {
            // Find the approval request ID from the leave data
            const leaveData = data?.data.find(
              (leave) => leave.id === params.leaveId,
            );
            const approvalRequestId =
              params.approvalRequestId ||
              leaveData?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID is required");
            }

            // Use the unified approval function
            await approveLeaveRequest(approvalRequestId, params.comment || "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
      rejectLeave: {
        updateFn: (data, params) => {
          if (typeof params === "string" || !params.leaveId) {
            return data;
          }

          const targetLeave = data?.data.find(
            (leave) => leave.id === params.leaveId,
          );
          const approvalRequestId =
            targetLeave?.relationships?.approval_request?.data?.id;

          return data
            ? ({
                ...data,
                data: data.data.map((leave) =>
                  leave.id === params.leaveId
                    ? {
                        ...leave,
                        attributes: {
                          ...leave.attributes,
                          status: "rejected",
                        },
                      }
                    : leave,
                ),
                included:
                  data.included?.map((item) => {
                    if (
                      item.type === "approval_request" &&
                      item.id === approvalRequestId
                    ) {
                      const approvalItem = item as ApprovalRequestData;
                      const currentStep = approvalItem.attributes.current_step;
                      const steps = approvalItem.attributes.steps || [];

                      // Create new action for the current step
                      const newAction = {
                        id: `temp-reject-${params.leaveId}`, // Stable temporary ID for optimistic update
                        user_id: "current_user", // This will be replaced by server response
                        action: "reject" as const,
                        comment:
                          typeof params === "string"
                            ? ""
                            : params.comment || "",
                        created_at: "pending", // Will be replaced by server response
                      };

                      // Update the steps array - mark current step as rejected
                      const updatedSteps = steps.map((step: ApprovalStep) => {
                        if (step.id === currentStep?.id) {
                          return {
                            ...step,
                            complete: false,
                            rejected: true,
                            actions: [...(step.actions || []), newAction],
                          };
                        }
                        return step;
                      });

                      return {
                        ...approvalItem,
                        attributes: {
                          ...approvalItem.attributes,
                          status: "rejected",
                          current_step: null, // Mark as completed (rejected)
                          steps: updatedSteps,
                        },
                      };
                    }
                    return item;
                  }) || [],
              } as EmployeeLeavesResponse)
            : data;
        },
        mutationFn: async (params) => {
          if (typeof params === "string" || !params.leaveId) {
            throw new Error("Invalid parameters for rejecting leave");
          }

          try {
            // Find the approval request ID from the leave data
            const leaveData = data?.data.find(
              (leave) => leave.id === params.leaveId,
            );
            const approvalRequestId =
              params.approvalRequestId ||
              leaveData?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID is required");
            }

            // Use the unified rejection function
            await rejectLeaveRequest(approvalRequestId, params.comment || "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
    },
    defaultData: {
      data: [],
      meta: { pagination: { count: 0, page: 1, limit: 5, from: 0, to: 0 } },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      keepPreviousData: true,
    },
  });

  const withdrawLeave = async (leaveId: string) => {
    try {
      await mutateData("withdrawLeave", leaveId);
      return true;
    } catch (error) {
      return false;
    }
  };

  const updateLeaveDates = async (
    leaveId: string,
    startDate: string,
    endDate: string,
    leaveDuration?: string,
    leaveType?: string,
  ) => {
    try {
      await mutateData("updateDates", {
        leaveId,
        startDate,
        endDate,
        leaveDuration,
        leaveType,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  const acceptLeave = async (leaveId: string) => {
    try {
      await mutateData("acceptLeave", {
        leaveId,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  const rejectLeave = async (leaveId: string) => {
    try {
      await mutateData("rejectLeave", {
        leaveId,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    leaveDetails: data?.data || [],
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    isWithdrawing: isWithdrawing || isWithdrawingFromHook, // Combine both loading states
    isUpdatingDates,
    isAccepting: isApproving, // Use the isApproving state from the unified hook
    isRejecting, // Use the isRejecting state from the unified hook
    withdrawLeave,
    updateLeaveDates,
    acceptLeave,
    rejectLeave,
    mutateData,
  };
};
