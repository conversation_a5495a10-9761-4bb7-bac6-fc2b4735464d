import { z } from "zod";
import { TFunction } from "@/types";

// Define the assignment schema for dynamic project and role IDs
export const assignmentSchema = (t: TFunction) => {
  return z.object({
    project: z.string({
      required_error: t("common.form.assignment.project.required"),
    }),
    role: z.string({
      required_error: t("common.form.assignment.role.required"),
    }),
  });
};

export type AssignmentSchemaType = z.infer<ReturnType<typeof assignmentSchema>>;
