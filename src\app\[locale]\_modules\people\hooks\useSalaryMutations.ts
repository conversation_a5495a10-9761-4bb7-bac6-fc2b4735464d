"use client";

import { useOptimisticMutation } from "@/hooks/ues-optimistic-mutatation";
import { useTranslations } from "next-intl";
import { useState } from "react";
import { SalaryCalculationApiResponse } from "../type/employees-salaries";
import { SALARY_STATUS } from "@/constants/enum";
import { updateSalaryStatus as updateSalaryStatusAction } from "../actions/update-salary-status";
import { useToastMessage } from "@/hooks/use-toast-message";
import { fetcher } from "@/services/fetcher";
import { useApprovalMutations } from "./useApprovalMutations";
import { mutate } from "swr";

interface UseSalaryMutationsProps {
  employeeId?: string;
  page?: string | number;
  limit?: string | number;
  onSuccess?: () => void;
}

export const useSalaryMutations = ({
  employeeId,
  page = 1,
  limit = 5,
  onSuccess,
}: UseSalaryMutationsProps = {}) => {
  const { showToast } = useToastMessage();
  const t = useTranslations();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Use the generic approval mutations hook for approve/reject
  const { approveRequest, rejectRequest, isApproving, isRejecting } =
    useApprovalMutations("salary");

  // Build the API URL based on whether we have an employeeId
  const apiUrl = employeeId
    ? `/api/finance/salary_calculations?sort=-1&filter[employee_id_eq]=${employeeId}&page=${page}&limit=${limit}&include=employee`
    : `/api/finance/salary_calculations?page=${page}&limit=${limit}`;

  // Use the optimistic mutation hook
  const { data, error, isLoading, mutateData } = useOptimisticMutation<
    SalaryCalculationApiResponse,
    { salaryId: string; status: SALARY_STATUS }
  >({
    key: apiUrl,
    fetcher,
    mutations: {
      submitSalary: {
        updateFn: (currentData, { salaryId }) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((salary) =>
              salary.id === salaryId
                ? {
                    ...salary,
                    attributes: {
                      ...salary.attributes,
                      status: SALARY_STATUS.SUBMITTED,
                    },
                  }
                : salary,
            ),
          };
        },
        mutationFn: async ({ salaryId }) => {
          setIsSubmitting(true);
          try {
            const result = await updateSalaryStatusAction(
              salaryId,
              SALARY_STATUS.SUBMITTED,
            );

            if (result.error) {
              throw new Error(result.error);
            }

            showToast(
              "success",
              t(
                "people.employees-page.profile.salary.table.actions.submit-success",
              ),
            );

            // Call onSuccess callback if provided
            if (onSuccess) {
              onSuccess();
            }

            // Return undefined to keep the optimistic update
            return { data: undefined };
          } catch (error) {
            showToast(
              "error",
              t(
                "people.employees-page.profile.salary.table.actions.submit-error",
              ),
            );
            throw error;
          } finally {
            setIsSubmitting(false);
          }
        },
      },
      approveSalary: {
        updateFn: (currentData, { salaryId }) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((salary) =>
              salary.id === salaryId
                ? {
                    ...salary,
                    attributes: {
                      ...salary.attributes,
                      status: SALARY_STATUS.APPROVED,
                    },
                  }
                : salary,
            ),
          };
        },
        mutationFn: async ({ salaryId }) => {
          try {
            // Find the approval request ID from the salary calculation
            const salaryCalculation = data?.data.find(
              (salary) => salary.id === salaryId,
            );
            const approvalRequestId =
              salaryCalculation?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID not found");
            }

            // Use the generic approval hook
            await approveRequest(approvalRequestId, "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
      rejectSalary: {
        updateFn: (currentData, { salaryId }) => {
          if (!currentData) return currentData;

          return {
            ...currentData,
            data: currentData.data.map((salary) =>
              salary.id === salaryId
                ? {
                    ...salary,
                    attributes: {
                      ...salary.attributes,
                      status: SALARY_STATUS.REJECTED,
                    },
                  }
                : salary,
            ),
          };
        },
        mutationFn: async ({ salaryId }) => {
          try {
            // Find the approval request ID from the salary calculation
            const salaryCalculation = data?.data.find(
              (salary) => salary.id === salaryId,
            );
            const approvalRequestId =
              salaryCalculation?.relationships?.approval_request?.data?.id;

            if (!approvalRequestId) {
              throw new Error("Approval request ID not found");
            }

            // Use the generic rejection hook
            await rejectRequest(approvalRequestId, "");

            return { data: undefined };
          } catch (error) {
            throw error;
          }
        },
      },
    },
    swrOptions: {
      dedupingInterval: 2000,
      revalidateAfterMutation: false,
      keepPreviousData: true,
    },
  });

  // Helper functions to call the mutations
  const submitSalary = async (salaryId: string) => {
    try {
      // First perform the optimistic update
      await mutateData("submitSalary", {
        salaryId,
        status: SALARY_STATUS.SUBMITTED,
      });
      // Then call the API to update the salary status
      await mutate(apiUrl);

      return true;
    } catch (error) {
      return false;
    }
  };

  const approveSalary = async (salaryId: string) => {
    try {
      // Simply call mutateData with the mutation name and parameters
      await mutateData("approveSalary", {
        salaryId,
        status: SALARY_STATUS.APPROVED,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  const rejectSalary = async (salaryId: string) => {
    try {
      // The mutation function will handle finding the approval request ID
      await mutateData("rejectSalary", {
        salaryId,
        status: SALARY_STATUS.REJECTED,
      });
      return true;
    } catch (error) {
      return false;
    }
  };

  return {
    SalaryStatus: data?.data || [],
    employeesData: data.included,
    pagination: data?.meta?.pagination,
    isLoading,
    error,
    submitSalary,
    approveSalary,
    rejectSalary,
    isSubmitting,
    isApproving,
    isRejecting,
  };
};
