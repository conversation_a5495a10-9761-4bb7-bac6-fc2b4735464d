"use client";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef, Table, TableMeta } from "@tanstack/react-table";
import { Locale } from "@/i18n/routing";
import { formatDate } from "@/lib/dateFormatter";
import { TFunction } from "@/types";
import { ApprovalRequestData } from "../../../type/approval-request";
import RequestStatus from "@/components/status/request-status";
import { ApprovalRequestsActions } from "./approval-requests-actions";

// Define the translation function type
interface TableMetaWithTranslation extends TableMeta<ApprovalRequestData> {
  t: TFunction;
  locale?: Locale;
  onShowDetails: (approvalRequest: ApprovalRequestData) => void;
  onAcceptRequest: (approvalRequest: ApprovalRequestData) => void;
  onRejectRequest: (approvalRequest: ApprovalRequestData) => void;
}

// Helper function to get meta data
const getMeta = (table: Table<ApprovalRequestData>) =>
  table.options.meta as TableMetaWithTranslation;

export const columns: ColumnDef<ApprovalRequestData>[] = [
  // Selection Checkbox
  {
    id: "select",
    enableSorting: false,
    enableHiding: false,
    header: ({ table }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#ACB5BB] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        className="w-[11px] h-[11px] rounded-[3px] border-[#1A1C1E] [&>*:last-child]:scale-[0.5] [&>*:last-child]:stroke-[1.5] flex items-center justify-center p-0"
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
  },

  // Employee Name (Requestor)
  {
    accessorKey: "requestor.name",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.approval-requests-page.table.columns.employeeName",
          )}
        </div>
      );
    },
    cell: ({ row }) => {
      const requestorName = row.original.attributes.requestor.name;
      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {requestorName}
        </p>
      );
    },
    meta: { filterVariant: "select" },
  },

  // Request Type
  {
    accessorKey: "approvable.type",
    header: ({ table }) => {
      return (
        <div className="text-start">
          {getMeta(table).t(
            "people.approval-requests-page.table.columns.requestType",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { t } = getMeta(table);
      const requestType = row.original.attributes.approvable.type;
      
      // Map the request type to a translation key
      const getRequestTypeTranslation = (type: string) => {
        switch (type.toLowerCase()) {
          case "leave":
            return t("people.approval-requests-page.table.requestTypes.leave");
          case "salary":
            return t("people.approval-requests-page.table.requestTypes.salary");
          default:
            return type;
        }
      };

      return (
        <p className="text-sm font-semibold text-start text-gray-500">
          {getRequestTypeTranslation(requestType)}
        </p>
      );
    },
  },

  // Request Date
  {
    accessorKey: "created_at",
    header: ({ table }) => {
      return (
        <div>
          {getMeta(table).t(
            "people.approval-requests-page.table.columns.requestDate",
          )}
        </div>
      );
    },
    cell: ({ row, table }) => {
      const { locale } = getMeta(table);
      const dateStr = row.original.attributes.created_at;
      const formattedDate = dateStr && formatDate(dateStr, locale ?? "en");

      return (
        <p className="text-sm font-semibold text-gray-500">{formattedDate}</p>
      );
    },
  },

  // Approval Status
  {
    accessorKey: "current_step",
    header: ({ table }) =>
      getMeta(table).t("people.approval-requests-page.table.columns.approvalStatus"),
    cell: ({ row }) => {
      const { current_step, steps, status } = row.original.attributes;

      // If no current step, the request is completed (approved/rejected)
      if (!current_step) {
        const finalStatus = status === "approved" ? "approved" : "rejected";
        return (
          <div className="mx-auto flex justify-center">
            <RequestStatus 
              groups={[{
                name: finalStatus === "approved" ? "تم القبول" : "تم الرفض",
                approved: finalStatus === "approved" ? 1 : 0,
                total: 1,
                status: finalStatus
              }]} 
            />
          </div>
        );
      }

      // Show current step progress
      const approvedCount = current_step.actions?.filter(
        (action) => action.action === "approve"
      ).length || 0;

      const rejected = current_step.actions?.some(
        (action) => action.action === "reject"
      );

      const stepStatus: "approved" | "pending" | "rejected" = rejected
        ? "rejected"
        : current_step.complete
          ? "approved"
          : "pending";

      const group = [{
        name: current_step.name,
        approved: approvedCount,
        total: current_step.actions?.length || 1,
        status: stepStatus,
      }];

      return (
        <div className="mx-auto flex justify-center">
          <RequestStatus groups={group} />
        </div>
      );
    },
  },

  // Actions Column
  {
    accessorKey: "actions",
    id: "actions",
    enableHiding: false,
    header: ({ table }) => getMeta(table).t("cm.table.columns.actions"),
    cell: ({ row, table }) => {
      const {
        onShowDetails,
        onAcceptRequest,
        onRejectRequest,
      } = getMeta(table);
      
      return (
        <div className="mx-auto flex justify-center">
          <ApprovalRequestsActions
            row={row}
            onAccept={onAcceptRequest}
            onReject={onRejectRequest}
            onShowDetails={onShowDetails}
          />
        </div>
      );
    },
  },
];
