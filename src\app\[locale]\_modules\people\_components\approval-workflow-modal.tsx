import React from "react";
import ResponsiveDialog from "@/components/responsive-dialog";
import { useApprovalWorkflow } from "../hooks/useApprovalWorkflow";
import { LeaveDetail, TIncludedEmployee } from "../type/employee-leaves";
import ApprovalStepsIndicator from "@/components/approval-steps-indicator";
import { useTranslations } from "next-intl";
import { DialogTitle } from "@/components/ui/dialog";
import { ApprovalRequestData } from "../type/approval-request";

type ApprovalWorkflowModalProps = {
  isOpen: boolean;
  onClose: () => void;
  leaveDetail: LeaveDetail;
  included: (TIncludedEmployee | ApprovalRequestData)[];
};

const ApprovalWorkflowModal = ({
  isOpen,
  onClose,
  leaveDetail,
  included,
}: ApprovalWorkflowModalProps) => {
  const { steps, approvalRequest, currentStepId } = useApprovalWorkflow(
    leaveDetail,
    included,
  );
  const t = useTranslations();

  const status = approvalRequest?.attributes.status;

  const getStatus = () => {
    if (status === "approved") return "approved";
    if (status === "rejected") return "rejected";
    return "pending";
  };

  const header = (
    <div className="px-6 py-[22px] border-b border-gray-100">
      <DialogTitle className="text-lg font-semibold text-right">
        {t(
          "people.leaves-requests-page.request-details.approval-workflow.title",
        )}
      </DialogTitle>
    </div>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={onClose}
      closeBtnStyle="top-[22px]"
      header={header}
      className="max-w-full sm:min-w-[469px] w-auto"
    >
      <>
        {steps && steps.length > 0 ? (
          <ApprovalStepsIndicator
            steps={steps}
            currentStepId={currentStepId}
            status={getStatus()}
            showTitle={false}
          />
        ) : (
          <div className="text-center py-8 text-gray-500">
            {t(
              "people.leaves-requests-page.request-details.approval-workflow.no-steps",
            )}
          </div>
        )}
      </>
    </ResponsiveDialog>
  );
};

export default ApprovalWorkflowModal;
