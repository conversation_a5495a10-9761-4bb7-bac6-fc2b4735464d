import { But<PERSON> } from "@/components/ui/button";
import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { X } from "lucide-react";
import { LeaveDetail, TIncludedEmployee } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";
import { findEmployeeById } from "../../../utils/find-employee";
import { mapRequestStatusToCanonical } from "@/constants/translations-mapping";
import { REQUEST_STATUS } from "@/constants/enum";
import RequestStatus from "@/components/status/request-status";

type LeavesRequestsActionsProps<TData> = {
  row: Row<TData>;
  onShowDetails: (value: TD<PERSON>, employee: TEmployee) => void;
  onAccept: (value: TData, employee: TEmployee) => void;
  onReject: (value: TD<PERSON>, employee: TEmployee) => void;
  employeeData: TIncludedEmployee[];
  // onShowNote: (value: TD<PERSON>, employee: TEmployee) => void;
};

const LeavesRequestsActions = <TData extends LeaveDetail>({
  row,
  onShowDetails,
  employeeData,
  onAccept,
  onReject,
}: LeavesRequestsActionsProps<TData>) => {
  const t = useTranslations();
  const employeeId = row.original.relationships.employee.data.id;
  const employee = findEmployeeById(employeeData, employeeId);

  const status = row.original.attributes.status as REQUEST_STATUS;
  const final = mapRequestStatusToCanonical(status);

  if (!employee) {
    return null;
  }

  const isWaiting = row.original.attributes.status === "pending";

  return (
    <div className="flex items-center gap-2 min-w-[116px]">
      {/* Show final status only if not pending */}
      {status !== "pending" && (
        <RequestStatus
          status={final}
          label={t(`common.status.request-status.${final}`)}
        />
      )}

      {/* Accept Button - only show if status is waiting */}
      {isWaiting && (
        <Button
          variant="outline"
          onClick={() => onAccept(row.original, employee)}
          className="h-9 min-w-[72px] p-0 text-black text-xs font-medium rounded-lg"
          title={t("people.leaves-requests-component.actions.accept")}
        >
          {t("people.leaves-requests-component.actions.accept")}
        </Button>
      )}

      {/* Reject Button - only show if status is waiting */}
      {isWaiting && (
        <Button
          variant="outline"
          onClick={() => onReject(row.original, employee)}
          className="h-9 w-9 p-0 text-error hover:text-red-600 text-xs font-medium rounded-lg"
          title={t("people.leaves-requests-component.actions.reject")}
        >
          {<X className="!w-5 !h-5" />}
        </Button>
      )}
    </div>
  );
};

export { LeavesRequestsActions };
