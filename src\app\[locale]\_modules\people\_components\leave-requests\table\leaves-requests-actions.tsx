import { But<PERSON> } from "@/components/ui/button";
import { Row } from "@tanstack/react-table";
import { useTranslations } from "next-intl";
import { X } from "lucide-react";
import { LeaveDetail, TIncludedEmployee } from "../../../type/employee-leaves";
import { TEmployee } from "../../../type/employee";
import { findEmployeeById } from "../../../utils/find-employee";
import { mapRequestStatusToCanonical } from "@/constants/translations-mapping";
import { REQUEST_STATUS } from "@/constants/enum";
import RequestStatus from "@/components/status/request-status";
import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";
import { ApprovalRequestData } from "../../../type/approval-request";
import { getIncludedItem } from "../../../utils/included-data/getIncludedItem";

type LeavesRequestsActionsProps<TData> = {
  row: Row<TData>;
  onShowDetails: (value: <PERSON><PERSON>, employee: TEmployee) => void;
  onAccept: (value: TD<PERSON>, employee: TEmployee) => void;
  onReject: (value: TData, employee: TEmployee) => void;
  employeeData: TIncludedEmployee[];
  includedData?: (TIncludedEmployee | ApprovalRequestData)[];
  // onShowNote: (value: TData, employee: TEmployee) => void;
};

// Utility function to check if current user has already acted on the current step
const hasUserAlreadyActed = (
  approvalRequest: ApprovalRequestData | undefined,
  currentUserId: number,
): { hasActed: boolean; action?: "approve" | "reject"; canAct: boolean } => {
  // If no approval request or no current user, can't act
  if (!approvalRequest || !currentUserId) {
    return { hasActed: false, canAct: false };
  }

  const { current_step, status } = approvalRequest.attributes;

  console.log(current_step);

  // If request is already completed (no current step), no one can act
  if (!current_step || status === "approved" || status === "rejected") {
    return { hasActed: false, canAct: false };
  }

  // If current step is already complete or rejected, no one can act on it
  if (current_step.complete || current_step.rejected) {
    return { hasActed: false, canAct: false };
  }

  // Check if current user has already acted on THIS current step
  const userAction = current_step.actions?.find(
    (action) => action.user_id === currentUserId.toString(),
  );

  return {
    hasActed: !!userAction,
    action: userAction?.action,
    canAct: !userAction, // Can act only if user hasn't acted yet
  };
};

const LeavesRequestsActions = <TData extends LeaveDetail>({
  row,
  onShowDetails,
  employeeData,
  onAccept,
  onReject,
  includedData,
}: LeavesRequestsActionsProps<TData>) => {
  const t = useTranslations();
  const { profile: currentEmployee } = useCurrentEmployee();
  const employeeId = row.original.relationships.employee.data.id;
  const employee = findEmployeeById(employeeData, employeeId);

  const status = row.original.attributes.status as REQUEST_STATUS;
  const final = mapRequestStatusToCanonical(status);

  // Get current user ID from employee.attributes.user_id
  const currentUserId = currentEmployee?.attributes?.user_id;

  console.log(currentUserId);

  // Get approval request data from included data
  const approvalRequestId =
    row.original.relationships.approval_request?.data?.id;
  const approvalRequest = approvalRequestId
    ? (getIncludedItem(
        includedData ?? [],
        "approval_request",
        approvalRequestId,
      ) as ApprovalRequestData | undefined)
    : undefined;

  // Check if current user has already acted on this step
  const {
    hasActed,
    action: userAction,
    canAct,
  } = hasUserAlreadyActed(approvalRequest, currentUserId || 0);

  if (!employee) {
    return null;
  }

  const isWaiting = row.original.attributes.status === "pending";

  // Get status message for user feedback
  const getStatusMessage = () => {
    if (!isWaiting) return null;
    if (hasActed) {
      return userAction === "approve"
        ? t("people.leaves-requests-component.actions.alreadyApproved")
        : t("people.leaves-requests-component.actions.alreadyRejected");
    }
    if (!canAct && approvalRequest?.attributes.current_step) {
      return t("people.leaves-requests-component.actions.stepNotAvailable");
    }
    return null;
  };

  const statusMessage = getStatusMessage();

  return (
    <div className="flex items-center gap-2 min-w-[116px]">
      {/* Show final status only if not pending */}
      {status !== "pending" && (
        <RequestStatus
          status={final}
          label={t(`common.status.request-status.${final}`)}
        />
      )}

      {/* Show status message if user has already acted */}
      {statusMessage && (
        <span className="text-xs text-gray-500 px-2 py-1 bg-gray-100 rounded">
          {statusMessage}
        </span>
      )}

      {/* Accept Button - only show if status is waiting and user can act */}
      {isWaiting && canAct && (
        <Button
          variant="outline"
          onClick={() => onAccept(row.original, employee)}
          className="h-9 min-w-[72px] p-0 text-black text-xs font-medium rounded-lg"
          title={t("people.leaves-requests-component.actions.accept")}
        >
          {t("people.leaves-requests-component.actions.accept")}
        </Button>
      )}

      {/* Reject Button - only show if status is waiting and user can act */}
      {isWaiting && canAct && (
        <Button
          variant="outline"
          onClick={() => onReject(row.original, employee)}
          className="h-9 w-9 p-0 text-error hover:text-red-600 text-xs font-medium rounded-lg"
          title={t("people.leaves-requests-component.actions.reject")}
        >
          {<X className="!w-5 !h-5" />}
        </Button>
      )}
    </div>
  );
};

export { LeavesRequestsActions };
