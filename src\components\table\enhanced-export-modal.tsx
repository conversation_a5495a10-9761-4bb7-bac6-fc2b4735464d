"use client";

import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import ResponsiveDialog from "@/components/responsive-dialog";
import { Indicator, type IndicatorStep } from "@/components/ui/step-indicator";
import { useTranslations } from "next-intl";
import type { ExportFormat, ExportFilters } from "@/types/export";
import { isDevelopment } from "@/utils/env";
import { CSVEXE, PDFEXE, XSLEXE } from "../../../public/images/icons";
import { SortableList } from "../dnd/SortableList";
import { SortableItem } from "../dnd/SortableItem";
import { GripVertical } from "lucide-react";

export type ExportColumn = {
  id: string;
  label: string;
  description?: string;
  required?: boolean;
  category?: string;
};

const getRequiredColumns = (columns: ExportColumn[]): string[] =>
  columns.filter((col) => col.required).map((col) => col.id);

const toggleColumnSelection = (
  columnId: string,
  selectedColumns: string[],
): string[] =>
  selectedColumns.includes(columnId)
    ? selectedColumns.filter((id) => id !== columnId)
    : [...selectedColumns, columnId];

interface EnhancedExportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onExport: (
    format: ExportFormat,
    selectedColumns: string[],
    filters?: ExportFilters,
  ) => Promise<void>;
  columns: ExportColumn[];
  supportedFormats: ExportFormat[];
  entityName: string;
  loading?: boolean;
  filters?: ExportFilters;
  defaultSelectedColumns?: string[];
}

export function EnhancedExportModal({
  isOpen,
  onClose,
  onExport,
  columns,
  supportedFormats,
  entityName,
  loading = false,
  filters,
  defaultSelectedColumns,
}: EnhancedExportModalProps) {
  const t = useTranslations();
  const [step, setStep] = useState<"columns" | "format">("columns");
  const [selectedFormat, setSelectedFormat] = useState<ExportFormat | null>(
    null,
  );
  const [selectedColumns, setSelectedColumns] = useState<string[]>(
    defaultSelectedColumns ||
      columns.filter((col) => !col.required).map((col) => col.id),
  );
  const [orderedColumns, setOrderedColumns] = useState<ExportColumn[]>(columns);

  useEffect(() => {
    setOrderedColumns(columns);
  }, [columns]);

  const requiredColumns = getRequiredColumns(columns);
  const allSelectedColumns = [...requiredColumns, ...selectedColumns];

  const steps: IndicatorStep[] = [
    {
      id: "columns",
      label: t("people.export.steps.selectFields") || "Select Fields",
    },
    {
      id: "format",
      label: t("people.export.steps.selectFormat") || "Select Format",
    },
  ];

  const handleExport = async () => {
    if (!selectedFormat) return;

    try {
      const orderedSelectedColumns = orderedColumns
        .filter((col) => allSelectedColumns.includes(col.id))
        .map((col) => col.id);

      await onExport(selectedFormat, orderedSelectedColumns, filters);
      handleClose();
    } catch (error) {
      if (isDevelopment()) console.error("Export failed:", error);
    }
  };

  const handleClose = () => {
    onClose();
    setStep("columns");
    setSelectedFormat(null);
  };

  const headerContent = (
    <>
      <DialogTitle className="px-6 py-[22px] border-b font-semibold text-[18px] leading-[28px] text-start">
        {t("people.export.title")}
      </DialogTitle>
      <DialogDescription className="sr-only">
        Export {entityName} data in your preferred format and columns
      </DialogDescription>
    </>
  );

  return (
    <ResponsiveDialog
      open={isOpen}
      onOpenChange={handleClose}
      header={headerContent}
      className={`max-w-xl ${step === "columns" ? "h-[90vh]" : ""}`}
      dismissible={false}
    >
      <Indicator steps={steps} currentStep={step} className="py-6" />

      <div className="flex flex-col flex-1">
        {step === "columns" && (
          <div className="pt-4 flex flex-col">
            <h2 className="text-right text-base font-medium leading-5 mb-4">
              {t("people.export.steps.selectFields")}
            </h2>
            <p className="text-right text-sm text-gray-600 mb-4">
              {t("people.export.dragToReorder")}
            </p>

            <SortableList items={orderedColumns} onReorder={setOrderedColumns}>
              <div className="space-y-4 mb-8">
                {orderedColumns.map((column) => {
                  const isSelected = allSelectedColumns.includes(column.id);
                  const isRequired = column.required;

                  return (
                    <SortableItem key={column.id} id={column.id}>
                      {({ listeners, attributes }) => (
                        <div
                          className={`relative border flex items-center gap-2 rounded-lg p-4 cursor-pointer transition-all flex-1 max-h-14 ${
                            isSelected
                              ? "border-slate-800 bg-slate-50"
                              : "border-slate-200 bg-white hover:border-slate-300"
                          }`}
                          onClick={() =>
                            !isRequired &&
                            setSelectedColumns((prev) =>
                              toggleColumnSelection(column.id, prev),
                            )
                          }
                        >
                          <Button
                            variant="ghost"
                            {...listeners}
                            {...attributes}
                            style={{ touchAction: "none" }}
                            className="cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600 px-0"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                            }}
                          >
                            <GripVertical className="!w-5 !h-5" />
                          </Button>
                          <div className="text-right text-sm font-medium text-slate-700">
                            {column.label}
                          </div>
                          {isRequired && (
                            <div className="absolute top-2 left-2">
                              <div className="w-2 h-2 bg-slate-400 rounded-full" />
                            </div>
                          )}
                        </div>
                      )}
                    </SortableItem>
                  );
                })}
              </div>
            </SortableList>
          </div>
        )}

        {step === "format" && (
          <div className="pt-4 flex flex-col">
            <h2 className="text-right text-base leading-5 font-medium mb-4">
              {t("people.export.chooseFormat")}
            </h2>
            <div className="grid grid-cols-3 gap-4 mb-8">
              {supportedFormats.map((format) => {
                const isSelected = selectedFormat === format;
                const formatColors = {
                  csv: "border-blue-600",
                  pdf: "border-red-500",
                  xlsx: "border-green-500",
                  svg: "border-orange-500",
                  png: "border-blue-800",
                  doc: "border-blue-700",
                };
                const selectedColor =
                  formatColors[format as keyof typeof formatColors] ||
                  "border-blue-400";

                return (
                  <button
                    key={format}
                    onClick={() => setSelectedFormat(format)}
                    className={`relative sm:min-w-[161px] sm:min-h-[120px] p-6 rounded-2xl border-2 transition-all duration-200 bg-gray-50 hover:bg-gray-100 ${
                      isSelected
                        ? `${selectedColor} bg-white`
                        : "border-gray-200"
                    }`}
                  >
                    <div className="flex flex-col items-center justify-center">
                      {format === "csv" && <CSVEXE className="w-10 h-10" />}
                      {format === "pdf" && <PDFEXE className="w-10 h-10" />}
                      {format === "xlsx" && <XSLEXE className="w-10 h-10" />}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        )}

        <div className="sticky border-t border-t-slate-200 pt-2.5 bottom-0 left-0 w-full rounded-t-[18px] bg-white flex flex-row items-center justify-between gap-4 sm:gap-6">
          {step === "columns" ? (
            <div className="pt-4 w-full flex gap-4">
              <Button
                onClick={() => setStep("format")}
                disabled={allSelectedColumns.length === 0}
                className="flex-1 h-12 w-1/2"
              >
                {t("common.buttonText.next")}
              </Button>
              <Button
                variant="outline"
                onClick={handleClose}
                className="flex-1 h-12 w-1/2"
              >
                {t("common.buttonText.cancel")}
              </Button>
            </div>
          ) : (
            <div className="pt-4 w-full flex gap-4">
              <Button
                onClick={handleExport}
                disabled={loading || !selectedFormat}
                className="flex-1 h-12 w-1/2"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                    {t("people.export.exporting")}
                  </>
                ) : (
                  t("people.export.export")
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => setStep("columns")}
                className="flex-1 h-12 w-1/2"
              >
                {t("common.buttonText.back")}
              </Button>
            </div>
          )}
        </div>
      </div>
    </ResponsiveDialog>
  );
}
