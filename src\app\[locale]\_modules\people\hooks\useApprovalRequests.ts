import useSWR from "swr";
import { fetcher } from "@/services/fetcher";
import { ApprovalRequestsResponse } from "../type/approval-request";

type UseApprovalRequestsParams = {
  page?: number;
  limit?: number;
  sort?: string;
  search?: string;
  filters?: string;
};

export const useApprovalRequests = ({
  page = 1,
  limit = 10,
  sort = "-created_at",
  search = "",
  filters = "",
}: UseApprovalRequestsParams = {}) => {
  // Build the URL with query parameters
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    sort,
  });

  if (search) {
    params.append("search", search);
  }

  if (filters) {
    // Parse filters and add them to params
    const filterParams = new URLSearchParams(filters);
    filterParams.forEach((value, key) => {
      params.append(key, value);
    });
  }

  const url = `/api/approval-requests?${params.toString()}`;

  const { data, error, mutate, isLoading } = useSWR<ApprovalRequestsResponse>(
    url,
    fetcher,
  );

  return {
    approvalRequests: data?.data || [],
    meta: data?.meta,
    isLoading,
    error,
    mutate,
  };
};
