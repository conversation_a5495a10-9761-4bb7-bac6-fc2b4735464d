"use client";

import {
  useState,
  useActionState,
  useMemo,
  startTransition,
  useEffect,
} from "react";
import { useLocale, useTranslations } from "next-intl";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { TFunction } from "@/types";
import {
  deviceCommandSchema,
  DeviceCommandSchemaType,
} from "../../../../schemas/deviceCommandSchema";
import { executeDeviceCommand } from "../../../../actions/execute-device-command";
import { useAvailableCommands } from "../../../../hooks/devices/commands/useAvailableCommands";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Loader2 } from "lucide-react";

import { useToastMessage } from "@/hooks/use-toast-message";
import { LANGUAGES } from "@/constants/enum";
import DeviceCommandsSkeleton from "./device-commands-skeleton";
import DeviceCommandResult from "./device-command-result";
import DeviceCommandParameters from "./device-command-parameters";

type CommandParameter = {
  id: string;
  key: string;
  value: string;
};

type DeviceCommandsFormProps = {
  deviceId: string;
};

export default function DeviceCommandsForm({
  deviceId,
}: DeviceCommandsFormProps) {
  const t = useTranslations() as TFunction;
  const { showToast } = useToastMessage();
  const locale = useLocale();
  const isRTL = LANGUAGES.ARABIC == locale;
  const {
    commands,
    isLoading: isLoadingCommands,
    error: commandsError,
  } = useAvailableCommands(deviceId);

  const [parameters, setParameters] = useState<CommandParameter[]>([]);
  const [selectedCommand, setSelectedCommand] = useState<string>("");

  const [state, formAction, isPending] = useActionState(executeDeviceCommand, {
    success: "",
    error: "",
    issues: [],
    data: null,
  });

  const form = useForm<DeviceCommandSchemaType>({
    resolver: zodResolver(deviceCommandSchema(t)),
    defaultValues: {
      command: "",
      parameters: [],
    },
    mode: "all",
  });

  // Watch form values for validation
  const watchedCommand = form.watch("command");

  // Check if form is valid for submit button
  const isFormValid = useMemo(() => {
    const hasRequiredParameters =
      parameters.length === 0 ||
      parameters.every(
        (param) => param.key.trim() !== "" && param.value.trim() !== "",
      );
    return watchedCommand && hasRequiredParameters;
  }, [watchedCommand, parameters]);

  // Update parameters when command changes
  const handleCommandChange = (commandId: string) => {
    setSelectedCommand(commandId);
    form.setValue("command", commandId);

    // Find the selected command and set up its parameters
    const command = commands.find((cmd) => cmd.id === commandId);
    if (command?.attributes?.parameters) {
      const newParameters = Object.entries(command.attributes.parameters).map(
        ([paramName, paramConfig]) => ({
          id: paramName,
          key: paramName,
          value: paramConfig.default?.toString() || "",
        }),
      );
      setParameters(newParameters);
      form.setValue("parameters", newParameters);
    } else {
      setParameters([]);
      form.setValue("parameters", []);
    }
  };

  const addParameter = () => {
    const newParameter: CommandParameter = {
      id: Date.now().toString(),
      key: "",
      value: "",
    };
    const updatedParameters = [...parameters, newParameter];
    setParameters(updatedParameters);
    form.setValue("parameters", updatedParameters);
  };

  const removeParameter = (id: string) => {
    const updatedParameters = parameters.filter((param) => param.id !== id);
    setParameters(updatedParameters);
    form.setValue("parameters", updatedParameters);
  };

  const updateParameter = (
    id: string,
    field: "key" | "value",
    newValue: string,
  ) => {
    const updatedParameters = parameters.map((param) =>
      param.id === id ? { ...param, [field]: newValue } : param,
    );
    setParameters(updatedParameters);
    form.setValue("parameters", updatedParameters);
  };

  const handleSubmit = async (data: DeviceCommandSchemaType) => {
    const formData = new FormData();
    formData.append("deviceId", deviceId);
    formData.append("command", data.command);
    formData.append("parameters", JSON.stringify(parameters));
    startTransition(async () => {
      formAction(formData);
    });
  };

  useEffect(() => {
    if (state.success && !isPending) {
      showToast("success", state.success, t("common.toast.success"));
    }

    if (state.error && !isPending) {
      showToast("error", state.error, t("common.toast.error"));
    }
  }, [state, isPending, t]);
  // Show skeleton while loading commands
  if (isLoadingCommands) {
    return <DeviceCommandsSkeleton />;
  }

  return (
    <Card className="p-6 shadow-none">
      <h3 className={`text-lg font-semibold mb-6 text-start`}>
        {t("people.devices-page.device-details.tabs.commands")}
      </h3>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Command Selection */}
          <FormField
            control={form.control}
            name="command"
            render={({ field }) => (
              <FormItem>
                <FormLabel className={`block text-start`}>
                  {t("people.devices-page.device-commands.form.command.label")}
                </FormLabel>
                <Select
                  onValueChange={handleCommandChange}
                  value={selectedCommand}
                  disabled={isPending}
                >
                  <FormControl>
                    <SelectTrigger
                      lang={locale}
                      className={`h-12 sm:w-1/2 text-sm !font-semibold leading-5 !text-black`}
                    >
                      <SelectValue
                        className={`text-black !font-semibold`}
                        dir={isRTL ? "rtl" : "ltr"}
                        placeholder={t(
                          "people.devices-page.device-commands.form.command.placeholder",
                        )}
                      />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {commands.length === 0 ? (
                      <div className="p-4 text-center text-gray-500">
                        {t(
                          "people.devices-page.device-commands.form.command.no-commands",
                        )}
                      </div>
                    ) : (
                      commands.map((command) => (
                        <SelectItem key={command.id} value={command.id}>
                          <div className="text-start">
                            <div className="font-medium">
                              {command.attributes.display_name}
                            </div>
                            {command.attributes.description && (
                              <div className="text-sm text-gray-500">
                                {command.attributes.description}
                              </div>
                            )}
                          </div>
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                <FormMessage />
                {commandsError && (
                  <p className="text-sm text-error text-start">
                    {t(
                      "people.devices-page.device-commands.form.command.error.loading",
                    )}
                  </p>
                )}
              </FormItem>
            )}
          />

          {/* Parameters Section */}
          <DeviceCommandParameters
            parameters={parameters}
            onAddParameter={addParameter}
            onRemoveParameter={removeParameter}
            onUpdateParameter={updateParameter}
          />

          {/* Execute Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isPending || !isFormValid || isLoadingCommands}
              className="px-8 min-w-[120px] min-h-12"
            >
              {isPending ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t("people.devices-page.device-commands.form.executing")}
                </div>
              ) : (
                t("people.devices-page.device-commands.form.execute")
              )}
            </Button>
          </div>
        </form>
      </Form>

      {/* Result Section */}
      <DeviceCommandResult
        success={state.success}
        error={state.error}
        isPending={isPending}
      />
    </Card>
  );
}
