import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

// Currency Item Skeleton - matches CurrencyItem component
export const CurrencyItemSkeleton = () => (
  <Card className="shadow-sm max-h-[104px]">
    <CardContent className="text-start p-5">
      <Skeleton className="h-4 w-24 mb-2 bg-gray-200" />
      <Skeleton className="h-6 w-16 bg-gray-200" />
    </CardContent>
  </Card>
);

// Salary Package Card Skeleton - matches SalaryPackageCard component
export const SalaryPackageCardSkeleton = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {/* Total Package Summary Skeleton */}
        <Card className="border border-gray-200 rounded-2xl shadow-sm">
          <CardContent className="p-5 grid grid-cols-2 gap-10">
            <div className="flex flex-col items-start gap-4">
              <Skeleton className="h-4 w-32 mb-2 bg-gray-200" />
              <Skeleton className="h-8 w-24 bg-gray-200" />
            </div>

            <div className="justify-self-end">
              <Skeleton className="h-8 w-20 bg-gray-200 rounded-lg" />
            </div>

            {/* Effective Period Skeleton */}
            <div className="flex flex-col col-span-2 gap-4">
              <Skeleton className="h-4 w-28 mb-2 bg-gray-200" />
              <Skeleton className="h-6 w-48 bg-gray-200" />
            </div>
          </CardContent>
        </Card>

        {/* Breakdown: Base + Allowances Skeleton */}
        <div className="grid grid-cols-2 gap-4">
          <CurrencyItemSkeleton />
          <CurrencyItemSkeleton />
          <CurrencyItemSkeleton />
          <CurrencyItemSkeleton />
        </div>
      </div>
    </div>
  );
};

// Salary Package History Skeleton - matches SalaryPackageHistory component
export const SalaryPackageHistorySkeleton = () => {
  return (
    <Card className="border-none shadow-none">
      <CardHeader className="pb-4 ps-0">
        <CardTitle className="p-0 text-base font-semibold text-secondary">
          <Skeleton className="h-6 w-32 bg-gray-200" />
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 pt-0 border border-gray-200 rounded-2xl shadow-sm">
        <div className="overflow-x-auto">
          <table className="w-full min-w-[572px]">
            {/* Table Header Skeleton */}
            <thead>
              <tr className="border-b border-gray-200">
                {Array.from({ length: 6 }).map((_, index) => (
                  <th
                    key={index}
                    className="text-start py-3 px-2 text-sm font-medium text-gray-600"
                  >
                    <Skeleton className="h-4 w-16 bg-gray-200" />
                  </th>
                ))}
              </tr>
            </thead>
            {/* Table Body Skeleton */}
            <tbody>
              {Array.from({ length: 3 }).map((_, rowIndex) => (
                <tr
                  key={rowIndex}
                  className="border-b border-gray-100 hover:bg-gray-50"
                >
                  {Array.from({ length: 6 }).map((_, colIndex) => (
                    <td key={colIndex} className="py-4 px-2 text-center">
                      <Skeleton
                        className={`h-4 bg-gray-200 ${
                          colIndex === 0
                            ? "w-24"
                            : colIndex === 5
                              ? "w-20"
                              : "w-16"
                        }`}
                      />
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CardContent>
    </Card>
  );
};

// Main Salary Package Display Skeleton - matches SalaryPackageDisplay component
export const SalaryPackageDisplaySkeleton = () => {
  return (
    <div className="space-y-6">
      <SalaryPackageCardSkeleton />
      <SalaryPackageHistorySkeleton />
    </div>
  );
};

// Salary History Changes Modal Skeleton - matches the exact structure
export const SalaryHistoryChangesModalSkeleton = () => {
  return (
    <div className="space-y-6">
      {Array.from({ length: 3 }).map((_, index) => (
        <div
          key={index}
          className="animate-pulse border-b border-gray-100 pb-6 mb-6 last:border-b-0 last:pb-0 last:mb-0"
        >
          {/* Date and Total Row */}
          <div className="flex justify-between items-start mb-4">
            <div className="space-y-2">
              <Skeleton className="h-4 w-20 bg-gray-200" />
              <Skeleton className="h-5 w-32 bg-gray-200" />
            </div>
            <div className="space-y-2 text-start">
              <Skeleton className="h-4 w-24 bg-gray-200" />
            </div>
          </div>

          {/* Salary Breakdown - 4 items in horizontal row */}
          <div className="flex justify-between items-center gap-2 max-sm:flex-wrap">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className="flex items-center gap-2">
                <Skeleton className="h-4 w-20 bg-gray-200" />
                <Skeleton className="h-4 w-16 bg-gray-200" />
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

// Complete Salary Package Page Skeleton
export const SalaryPackagePageSkeleton = () => {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex items-center justify-between mb-4">
        <Skeleton className="h-8 w-48 bg-gray-200" />
        <Skeleton className="h-10 w-32 bg-gray-200 rounded-[9px]" />
      </div>

      {/* Content Skeleton */}
      <SalaryPackageDisplaySkeleton />
    </div>
  );
};
