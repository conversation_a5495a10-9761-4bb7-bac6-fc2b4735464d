import { ApprovalStatus } from "@/enums/procure";
import { Item, RejectedNote } from "@/types/procure";

export const tempApprovalItems = [
  {
    id: "1",
    title: "محمود عصام",
    subtitle: "مسؤول الموارد البشرية",
    status: ApprovalStatus.Accepted,
    date: "02 فبراير، 2025",
  },
  {
    id: "2",
    title: "عمر يوسف",
    subtitle: "مدير الماليات",
    status: ApprovalStatus.Accepted,
    date: "02 فبراير، 2025",
  },
  {
    id: "3",
    title: "محمد ثروت",
    subtitle: "المُحاسب",
    status: ApprovalStatus.Pending,
    date: "02 فبراير، 2025",
  },
  {
    id: "4",
    title: "احمد مبروك",
    subtitle: "مدير المشروعات",
    status: ApprovalStatus.Rejected,
    date: "02 فبراير، 2025",
  },
];

export const listSampleItems: Item[] = [
  {
    id: 1,
    name: "لاب توب HP",
    category: "الكترونيات",
    orderCount: 12,
    price: 304200,
  },
  {
    id: 2,
    name: "جهاز تابلت سامسونج",
    category: "الكترونيات",
    orderCount: 5,
    price: 500300,
  },
  {
    id: 3,
    name: "هاتف آيفون 13",
    category: "الكترونيات",
    orderCount: 8,
    price: 800400,
  },
  {
    id: 4,
    name: "ساعة ذكية فيتبيت",
    category: "الكترونيات",
    orderCount: 10,
    price: 200250,
  },
  {
    id: 5,
    name: "كاميرا كانون",
    category: "الكترونيات",
    orderCount: 15,
    price: 600150,
  },
  {
    id: 6,
    name: "سماعات بلوتوث",
    category: "الكترونيات",
    orderCount: 20,
    price: 350100,
  },
];

export const sampleNotes: RejectedNote[] = [
  {
    id: 1,
    orderNumber: "12346",
    itemName: "أجهزة كهربائية",
    rejectedBy: "أحمد سعيد",
    reason:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص",
  },
  {
    id: 2,
    orderNumber: "12347",
    itemName: "أجهزة كهربائية",
    rejectedBy: "أحمد سعيد",
    reason:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص",
  },
  {
    id: 3,
    orderNumber: "12348",
    itemName: "أجهزة كهربائية",
    rejectedBy: "أحمد سعيد",
    reason:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص",
  },
  {
    id: 4,
    orderNumber: "12349",
    itemName: "أجهزة كهربائية",
    rejectedBy: "أحمد سعيد",
    reason:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص",
  },
  {
    id: 5,
    orderNumber: "12350",
    itemName: "أجهزة كهربائية",
    rejectedBy: "أحمد سعيد",
    reason:
      "هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة. لقد تم توليد هذا النص من مولد النص العربي، حيث يمكنك أن تولد مثل هذا النص",
  },
];
