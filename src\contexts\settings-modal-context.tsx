"use client";

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  ReactNode,
  useCallback,
} from "react";
import { useRouter, usePathname } from "next/navigation";
import { useLocale } from "next-intl";
import { useSystem } from "./system-provider";
import { Locale } from "@/i18n/routing";

export type SettingsTab = "GENERAL" | "NOTIFICATIONS" | "HOLIDAYS";
export type SettingsSubForm = "EDIT_PROFILE" | "CHANGE_PASSWORD";

interface SettingsModalContextProps {
  openSettings: (tab: SettingsTab, subForm?: SettingsSubForm) => void;
  settingsModalOpen: boolean;
  setSettingsModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  settingsActiveTab: SettingsTab;
  settingsSubForm: SettingsSubForm | null;
}

const SettingsModalContext = createContext<
  SettingsModalContextProps | undefined
>(undefined);

export const SettingsModalProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const router = useRouter();
  const pathname = usePathname();
  const locale: Locale = useLocale() as Locale;
  const { currSystem } = useSystem();

  const [settingsModalOpen, setSettingsModalOpen] = useState<boolean>(false);
  const [settingsActiveTab, setSettingsActiveTab] =
    useState<SettingsTab>("GENERAL");
  const [settingsSubForm, setSettingsSubForm] =
    useState<SettingsSubForm | null>(null);

  // Save the previous path before entering settings
  const previousPathRef = useRef<string | null>(null);
  const targetMobilePath = `/${locale}/${currSystem}/settings`;

  const isMobileViewport = () =>
    typeof window !== "undefined" && window.innerWidth < 768;

  const openSettings = useCallback(
    (tab: SettingsTab, subForm?: SettingsSubForm) => {
      // Save the current path before switching to settings
      previousPathRef.current = pathname;
      setSettingsActiveTab(tab);
      if (subForm) {
        setSettingsSubForm(subForm);
      }

      if (isMobileViewport()) {
        // On mobile, if not already on the settings page, navigate there.
        if (pathname !== targetMobilePath) {
          router.push(targetMobilePath);
        }
        // Close the modal on mobile
        setSettingsModalOpen(false);
      } else {
        // On desktop, if already on the mobile settings page, close the modal.
        if (pathname === targetMobilePath) {
          setSettingsModalOpen(false);
        } else {
          // Otherwise, open the modal.
          setSettingsModalOpen(true);
        }
      }
    },
    [pathname, router, targetMobilePath],
  );

  // Listen to viewport changes and adjust the modal state accordingly.
  useEffect(() => {
    const handleResize = () => {
      if (isMobileViewport()) {
        // If the modal is open (desktop settings) and we switch to mobile,
        // close the modal and navigate to the mobile settings page.
        if (settingsModalOpen) {
          setSettingsModalOpen(false);
          router.push(targetMobilePath);
        }
      } else {
        // On desktop: if we are on the mobile settings page and have a previous route,
        // navigate back and re-open the modal.
        if (pathname === targetMobilePath && previousPathRef.current) {
          router.push(previousPathRef.current);
          setSettingsModalOpen(true);
        }
      }
    };

    window.addEventListener("resize", handleResize);
    // Run once on mount
    handleResize();

    return () => window.removeEventListener("resize", handleResize);
  }, [settingsModalOpen, pathname, router, targetMobilePath]);

  return (
    <SettingsModalContext.Provider
      value={{
        openSettings,
        settingsModalOpen,
        setSettingsModalOpen,
        settingsActiveTab,
        settingsSubForm,
      }}
    >
      {children}
    </SettingsModalContext.Provider>
  );
};

export const useSettingsModal = (): SettingsModalContextProps => {
  const context = useContext(SettingsModalContext);
  if (context === undefined) {
    throw new Error(
      "useSettingsModal must be used within a SettingsModalProvider",
    );
  }
  return context;
};
