"use client";

import { use<PERSON><PERSON>back, useEffect, useState, useMemo } from "react";
import Link from "next/link";
import dynamic from "next/dynamic";
import { useActionState, startTransition } from "react";
import { useLocale, useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { useRouter } from "next/navigation"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import Loader from "@/components/loader";

import {
  Pencil,
  Lock,
  SignOut,
  Settings as SettingsIcon,
  DownArrow,
} from "../../../public/images/icons";

import { LANGUAGES } from "@/constants/enum";
import { PermissionEnum } from "@/enums/Permission";

import { usePermission } from "@/contexts/PermissionContext";
import { useUser } from "@/contexts/user-provider";

import { useSettingsModal } from "@/contexts/settings-modal-context";
import { useToastMessage } from "@/hooks/use-toast-message";
import { onSubmitLogout } from "@/server/actions/auth";
import { performLogout } from "@/utils/logout";
import { Locale } from "@/i18n/routing";
import { ActionState } from "@/types";
import { getProfileHref } from "./utils/getProfileHref";
import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";

const SettingsModal = dynamic(() => import("./settings-modal"), {
  loading: () => <Loader overlayColor="#000" overlayOpacity={0.6} />,
  ssr: false,
});

export default function UserProfileButton() {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  const pathname = usePathname();
  const isAr = locale === LANGUAGES.ARABIC;
const router = useRouter();
  const { showToast } = useToastMessage();
  const { user, mutateUser } = useUser();
  const {
    hasPermission,
    clearPermissions,
    isLoading: permLoading,
  } = usePermission();

  const { profile, isLoading, error } = useCurrentEmployee();

  const canAccessAllEmployees = hasPermission(PermissionEnum.READ_EMPLOYEE);

  const initialState: ActionState<{ redirectTo: string }> = {
    error: "",
    success: "",
    issues: [],
    redirectTo: "",
  };
  const [state, logoutAction, isPending] = useActionState(
    onSubmitLogout,
    initialState,
  );

  const {
    openSettings,
    settingsModalOpen,
    setSettingsModalOpen,
    settingsActiveTab,
    settingsSubForm,
  } = useSettingsModal();

  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isNavigating, setIsNavigating] = useState(false);

  const toggleDropdown = useCallback(
    () => setIsDropdownOpen((prev) => !prev),
    [],
  );
  const employeeId = profile?.id;

  const profileHref = useMemo(() => {
    const href = getProfileHref({ locale, employeeId, hasPermission });

    return href || `/${locale}/people`;
  }, [locale, employeeId, hasPermission]);

  // Check if current path matches the profile destination
  const isOnProfilePage = useMemo(() => {
    return pathname === profileHref;
  }, [pathname, profileHref]);

  const handleLogout = () => {
    setIsDropdownOpen(false);
    startTransition(() => {
      logoutAction();
    });
  };


  // Reset navigation loading when pathname changes (navigation complete)
  useEffect(() => {
    setIsNavigating(false);
  }, [pathname]);

// After successful logout, show toast and redirect
  useEffect(() => {
    if (state.success) {
      clearPermissions();
      mutateUser(null);
      showToast("success", state.success);
      router.replace(state.redirectTo || `/${locale}/auth/login`);
    }
  }, [state.success, state.redirectTo, locale ,showToast]);


  // Loading or error states
  // const Loading = permLoading || !user || isLoading;

  if (!user) {
    return (
      <div className="h-10 flex gap-1 items-center">
        <Skeleton className="h-[38px] w-[38px] rounded-full bg-gray-200" />
        <div className="flex flex-col gap-1 text-start">
          <Skeleton className="h-3 w-20 bg-gray-200" />
        </div>
        <Skeleton className="h-6 w-6 bg-gray-200 rounded" />
      </div>
    );
  }

  return (
    <>
      {/* Backdrop for dropdown */}
      {isDropdownOpen && (
        <div
          className="fixed inset-0 bg-black/30 z-40"
          onClick={() => setIsDropdownOpen(false)}
        />
      )}

      <div className="flex items-center max-h-10">
        <Button
          asChild={!isOnProfilePage}
          variant="ghost"
          disabled={isLoading || isOnProfilePage || error}
          className={`max-h-10 justify-start min-w-[131px] min-h-10 h-full rounded-e-none focus-visible:ring-2 focus-visible:ring-offset-2 ${
            isOnProfilePage
              ? "cursor-default disabled:text-black disabled:opacity-100"
              : "hover:bg-gray-100 cursor-pointer"
          } ${isNavigating && "opacity-55 cursor-default"}`}
        >
          <Link href={profileHref} onClick={() => setIsNavigating(true)}>
            <div className="group flex items-center justify-start gap-1">
              <Avatar className="w-[38px] h-[38px] self-start shrink-0 transition-all duration-300">
                <AvatarImage
                  src={user.avatar || "/default-avatar.png"}
                  alt="User Avatar"
                  className="object-cover object-top rounded-full"
                />
                <AvatarFallback>{user.name?.charAt(0) || "U"}</AvatarFallback>
              </Avatar>
              <div className="flex flex-col leading-none text-start rtl:text-end flex-2 w-full">
                <span className={`font-bold text-xs`}>{user.name}</span>
              </div>
            </div>
          </Link>
        </Button>

        <DropdownMenu open={isDropdownOpen} onOpenChange={toggleDropdown}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="p-[11px] flex items-start group justify-center min-h-10 max-h-10 max-w-10 hover:bg-gray-100 rounded-lg rounded-s-none transition-colors focus-visible:ring-0 focus-outline-none"
            >
              <DownArrow
                className={`!w-6 !h-5 text-[#596375] group-hover:text-black transition-all ${isDropdownOpen ? "rotate-180" : ""}`}
              />
            </Button>
          </DropdownMenuTrigger>

          <DropdownMenuContent
            lang={locale}
            align={isAr ? "start" : "end"}
            className="w-60 z-50 flex flex-col gap-1 p-3 text-[#727A90] rounded-xl"
          >
            <DropdownMenuItem
              onClick={() => {
                openSettings("GENERAL", "EDIT_PROFILE");
                setIsDropdownOpen(false);
              }}
              className="rtl:flex-row-reverse hover:bg-background-v2 hover:text-secondary h-10 cursor-pointer"
            >
              <Pencil />
              {t("common.navbar.userProfile.editProfile")}
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={() => {
                openSettings("GENERAL", "CHANGE_PASSWORD");
                setIsDropdownOpen(false);
              }}
              className="rtl:flex-row-reverse hover:bg-background-v2 hover:text-secondary h-10 cursor-pointer"
            >
              <Lock className="!w-[18px] !h-6" />
              {t("common.navbar.userProfile.editPassword")}
            </DropdownMenuItem>

            <DropdownMenuItem
              onClick={() => {
                openSettings("NOTIFICATIONS");
                setIsDropdownOpen(false);
              }}
              className="rtl:flex-row-reverse hover:bg-background-v2 hover:text-secondary h-10 cursor-pointer"
            >
              <SettingsIcon />
              {t("common.navbar.userProfile.settings")}
            </DropdownMenuItem>

            <DropdownMenuItem
              disabled={isPending}
              onClick={() => {
                handleLogout();
                setIsDropdownOpen(false);
              }}
              className="rtl:flex-row-reverse text-error font-bold hover:bg-background-v2 hover:text-secondary h-10 cursor-pointer"
            >
              <SignOut />
              {t("common.navbar.userProfile.logout")}
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {settingsModalOpen && (
        <SettingsModal
          open={settingsModalOpen}
          activeTab={settingsActiveTab}
          subForm={settingsSubForm}
          openSettings={openSettings}
          onClose={() => setSettingsModalOpen(false)}
        />
      )}
    </>
  );
}
