"use client";

import { useState } from "react";
import { useTranslations } from "next-intl";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";
import { DataTable } from "@/components/ui/data-table";
import { useApprovalRequests } from "../../../hooks/useApprovalRequests";
import { useApprovalMutations } from "../../../hooks/useApprovalMutations";
import { ApprovalRequestData } from "../../../type/approval-request";
import { columns } from "./approval-requests-columns";

interface ApprovalRequestsTableProps {
  page?: number;
  limit?: number;
  search?: string;
  filters?: string;
}

export function ApprovalRequestsTable({
  page = 1,
  limit = 10,
  search = "",
  filters = "",
}: ApprovalRequestsTableProps) {
  const t = useTranslations();
  const locale = useLocale() as Locale;
  
  const [selectedRequest, setSelectedRequest] = useState<ApprovalRequestData | null>(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);

  // Fetch approval requests data
  const { 
    approvalRequests, 
    meta, 
    isLoading, 
    error, 
    mutate 
  } = useApprovalRequests({
    page,
    limit,
    search,
    filters,
  });

  // Approval mutations
  const {
    approveRequest,
    rejectRequest,
    isApproving,
    isRejecting,
  } = useApprovalMutations();

  const handleShowDetails = (approvalRequest: ApprovalRequestData) => {
    setSelectedRequest(approvalRequest);
    setShowDetailsModal(true);
  };

  const handleAcceptRequest = async (approvalRequest: ApprovalRequestData) => {
    try {
      await approveRequest(
        approvalRequest.id,
        "", // comment - could be enhanced to show a dialog for comment input
        () => {
          // On success, refetch the data
          mutate();
        },
        () => {
          // Optimistic update - could update the local state immediately
          console.log("Optimistically updating approval request");
        }
      );
    } catch (error) {
      console.error("Error approving request:", error);
    }
  };

  const handleRejectRequest = async (approvalRequest: ApprovalRequestData) => {
    try {
      await rejectRequest(
        approvalRequest.id,
        "", // comment - could be enhanced to show a dialog for comment input
        () => {
          // On success, refetch the data
          mutate();
        },
        () => {
          // Optimistic update - could update the local state immediately
          console.log("Optimistically updating approval request");
        }
      );
    } catch (error) {
      console.error("Error rejecting request:", error);
    }
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500">
          {t("people.approval-requests-page.table.error")}
        </p>
      </div>
    );
  }

  return (
    <>
      <DataTable
        data={approvalRequests}
        columns={columns}
        tableContainerClass="min-h-[240px]"
        title={t("people.approval-requests-page.table.title")}
        isLoading={isLoading}
        meta={{
          t,
          locale,
          onShowDetails: handleShowDetails,
          onAcceptRequest: handleAcceptRequest,
          onRejectRequest: handleRejectRequest,
        }}
        pagination={{
          pageIndex: (meta?.pagination.page || 1) - 1,
          pageSize: meta?.pagination.limit || limit,
          pageCount: Math.ceil((meta?.pagination.count || 0) / (meta?.pagination.limit || limit)),
        }}
      />

      {/* TODO: Add details modal component */}
      {showDetailsModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg max-w-2xl w-full mx-4">
            <h2 className="text-xl font-bold mb-4">
              {t("people.approval-requests-page.details.title")}
            </h2>
            <div className="space-y-4">
              <div>
                <strong>{t("people.approval-requests-page.details.requestor")}:</strong>{" "}
                {selectedRequest.attributes.requestor.name}
              </div>
              <div>
                <strong>{t("people.approval-requests-page.details.type")}:</strong>{" "}
                {selectedRequest.attributes.approvable.type}
              </div>
              <div>
                <strong>{t("people.approval-requests-page.details.status")}:</strong>{" "}
                {selectedRequest.attributes.status}
              </div>
              <div>
                <strong>{t("people.approval-requests-page.details.workflow")}:</strong>{" "}
                {selectedRequest.attributes.workflow_name}
              </div>
            </div>
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => setShowDetailsModal(false)}
                className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
              >
                {t("common.close")}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
