"use client";

import { onSubmitLogout } from "@/server/actions/auth";
import { deleteCookie } from "cookies-next";
import { clearClientSession } from "./logout";

export const handleTokenExpiration = async (locale: string = "en") => {
  // Clear client-side session data (permissions, etc.)
  clearClientSession();

  await onSubmitLogout();

  // Clear any system-specific tokens
  const systems = ["people", "procure", "cm"];
  systems.forEach((system) => {
    deleteCookie(`${system}_session_token`);
  });

  // Redirect to login page
  window.location.href = `/${locale}/auth/login`;
};

export const isTokenExpirationError = (error: any): boolean => {
  // Check for specific error messages
  if (typeof error === "string" && error.includes("Expired access token")) {
    return true;
  }

  if (error && error.message && typeof error.message === "string") {
    if (
      error.message.includes("Expired access token") ||
      error.message.includes("invalid token")
    ) {
      return true;
    }
  }

  // Check for error response data
  if (error && error.data && error.data.error) {
    const errorMessage = error.data.error;
    if (
      typeof errorMessage === "string" &&
      errorMessage.includes("Expired access token")
    ) {
      return true;
    }
  }

  return false;
};
