import { Suspense } from "react";
import { EmployeeAttendanceCards } from "../../../../_modules/people/_components/employees/profile";

export default async function EmployeeAttendancePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id: employeeId } = await params;

  return (
    <>
      <Suspense fallback={<div>{"Loading..."}</div>}>
        <EmployeeAttendanceCards employeeId={employeeId} />
      </Suspense>
    </>
  );
}
