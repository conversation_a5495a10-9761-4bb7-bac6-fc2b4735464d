import { TFunction } from "@/types";
import {
  Attendance_Event_type,
  LANGUAGES,
  MONTHS,
  PATIENT_STATUS,
  REQUEST_STATUS,
  SALARY_STATUS,
  DEVICE_STATUS,
} from "./enum";
import { MAIN_CATEGORY } from "@/app/[locale]/_modules/people/enum";
import {
  TLeaveType,
  TMainCategory,
} from "@/app/[locale]/_modules/people/type/leave-request";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";

export const getMonthTranslations = (month: string, t: TFunction) => {
  const monthMapping: Record<string, string> = {
    يناير: MONTHS.JANUARY,
    فبراير: MONTHS.FEBRUARY,
    مارس: MONTHS.MARCH,
    إبريل: MONTHS.APRIL,
    مايو: MONTHS.MAY,
    يونيو: MONTHS.JUNE,
    يوليو: MONTHS.JULY,
    أغسطس: MONTHS.AUGUST,
    سبتمبر: MONTHS.SEPTEMBER,
    أكتوبر: MONTHS.OCTOBER,
    نوفمبر: MONTHS.NOVEMBER,
    ديسمبر: MONTHS.DECEMBER,
  };

  // Ensure we get a valid string
  const englishMonth = String(monthMapping[month] ?? month);

  return t(`cm.appointmentchart.${englishMonth.toLowerCase()}`);
};

export const mapStatusToCanonical = (status: string): PATIENT_STATUS => {
  const mapping: Record<string, PATIENT_STATUS> = {
    معلقة: PATIENT_STATUS.PENDING,
    قادمة: PATIENT_STATUS.INCOMING,
    انتظار: PATIENT_STATUS.WAITING,
    تمت: PATIENT_STATUS.COMPLETED,
  };

  return mapping[status] || status;
};

export const mapRequestStatusToCanonical = (status: string): REQUEST_STATUS => {
  const mapping: Record<string, REQUEST_STATUS> = {
    مقبولة: REQUEST_STATUS.ACCEPTED,
    مرفوضة: REQUEST_STATUS.REJECTED,
    "قيد الانتظار": REQUEST_STATUS.WAITING,
    تمت: REQUEST_STATUS.COMPLETED,
    مسحوبة: REQUEST_STATUS.WITHDRAWN,
  };
  return mapping[status] || status;
};

export const mapSalaryStatusToCanonical = (status: string): SALARY_STATUS => {
  const mapping: Record<string, SALARY_STATUS> = {
    مدفوع: SALARY_STATUS.PAID,
    مرفوض: SALARY_STATUS.REJECTED,
    مسودة: SALARY_STATUS.DRAFT,
    موافقة: SALARY_STATUS.APPROVED,
    "تم التسليم": SALARY_STATUS.SUBMITTED,
  };
  return mapping[status] || status;
};

export const mapCheckinStatusToCanonical = (
  status: string,
): Attendance_Event_type => {
  const mapping: Record<string, Attendance_Event_type> = {
    دخول: Attendance_Event_type.CheckIn,
    خروج: Attendance_Event_type.CheckOut,
  };
  return mapping[status] || status;
};

export const mapDeviceStatusToCanonical = (status: string): DEVICE_STATUS => {
  const mapping: Record<string, DEVICE_STATUS> = {
    نشط: DEVICE_STATUS.ACTIVE,
    متوقف: DEVICE_STATUS.INACTIVE,
    صيانة: DEVICE_STATUS.MAINTENANCE,
    خطأ: DEVICE_STATUS.ERROR,
  };
  return mapping[status] || (status as DEVICE_STATUS);
};

export const getDayTranslations = (t: TFunction) => {
  const dayTranslations: { [key: string]: string } = {
    monday: t("common.days.monday"),
    tuesday: t("common.days.tuesday"),
    wednesday: t("common.days.wednesday"),
    thursday: t("common.days.thursday"),
    friday: t("common.days.friday"),
    saturday: t("common.days.saturday"),
    sunday: t("common.days.sunday"),
  };
  return dayTranslations;
};

export const getLeaveTypeTranslations = (t: TFunction) => {
  const leaveTypeTranslations: { [key: string]: string } = {
    annual: t(
      "people.distribution-leaves-chart.leave-distribution-xAxis-data.Annual",
    ),
    sick: t(
      "people.distribution-leaves-chart.leave-distribution-xAxis-data.Sick",
    ),
    marriage: t(
      "people.distribution-leaves-chart.leave-distribution-xAxis-data.Marriage",
    ),
    maternity:
      t(
        "people.distribution-leaves-chart.leave-distribution-xAxis-data.Maternity",
      ) || "Maternity",
    paternity:
      t(
        "people.distribution-leaves-chart.leave-distribution-xAxis-data.Paternity",
      ) || "Paternity",
    unpaid:
      t(
        "people.distribution-leaves-chart.leave-distribution-xAxis-data.Unpaid",
      ) || "Unpaid",
  };
  return leaveTypeTranslations;
};

export const getMainCategoryTranslation = (
  mainCategory: TMainCategory,
  t: TFunction,
): string => {
  if (mainCategory === MAIN_CATEGORY.LEAVE) {
    return t(
      "people.leaves-requests-component.leaves-requests.categories.leave",
    );
  } else if (mainCategory === MAIN_CATEGORY.OVERTIME) {
    return t("people.leaves-requests-component.tabs.overtime");
  }
  return "";
};

export const getLeaveRequestCategory = (
  mainCategory: TMainCategory,
  leaveType: TLeaveType | undefined,
  t: TFunction,
): string => {
  const mainCategoryTranslation = getMainCategoryTranslation(mainCategory, t);

  if (mainCategory === MAIN_CATEGORY.LEAVE && leaveType) {
    const leaveTypeTranslations = getLeaveTypeTranslations(t);
    const leaveTypeText = leaveTypeTranslations[leaveType];
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const locale: Locale = useLocale() as Locale;
    const isAr = locale === LANGUAGES.ARABIC;

    return isAr
      ? `${mainCategoryTranslation} - ${mainCategoryTranslation} ${leaveTypeText}`
      : `${mainCategoryTranslation} - ${leaveTypeText} ${mainCategoryTranslation}`;
  }

  return mainCategoryTranslation;
};
