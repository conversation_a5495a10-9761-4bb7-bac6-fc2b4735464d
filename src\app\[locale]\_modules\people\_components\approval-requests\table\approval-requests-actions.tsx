"use client";

import { Row } from "@tanstack/react-table";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreH<PERSON>zontal, Eye, Check, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { ApprovalRequestData } from "../../../type/approval-request";
import { useCurrentEmployee } from "@/hooks/people/getCurrentEmployee";

interface ApprovalRequestsActionsProps {
  row: Row<ApprovalRequestData>;
  onAccept: (approvalRequest: ApprovalRequestData) => void;
  onReject: (approvalRequest: ApprovalRequestData) => void;
  onShowDetails: (approvalRequest: ApprovalRequestData) => void;
}

// Utility function to check if current user has already acted on the current step
const hasUserAlreadyActed = (
  currentStep: any,
  currentUserId: number
): { hasActed: boolean; action?: "approve" | "reject" } => {
  if (!currentStep?.actions || !currentUserId) {
    return { hasActed: false };
  }

  // Compare employee.user_id (number) with action.user_id (string)
  const userAction = currentStep.actions.find(
    (action: any) => action.user_id === currentUserId.toString()
  );

  return {
    hasActed: !!userAction,
    action: userAction?.action,
  };
};

export function ApprovalRequestsActions({
  row,
  onAccept,
  onReject,
  onShowDetails,
}: ApprovalRequestsActionsProps) {
  const t = useTranslations();
  const approvalRequest = row.original;
  const { profile: currentEmployee } = useCurrentEmployee();
  
  // Get current user ID from employee.attributes.user_id
  const currentUserId = currentEmployee?.attributes?.user_id;
  
  // Check if the request can be acted upon (has current step and not completed)
  const canAct = approvalRequest.attributes.current_step && 
                 !approvalRequest.attributes.current_step.complete &&
                 !approvalRequest.attributes.current_step.rejected;

  // Check if current user has already acted on this step
  const { hasActed, action: userAction } = hasUserAlreadyActed(
    approvalRequest.attributes.current_step,
    currentUserId
  );

  // Determine if buttons should be disabled
  const shouldDisableActions = !canAct || hasActed;

  // Get status message for user feedback
  const getStatusMessage = () => {
    if (!canAct) {
      return t("people.approval-requests-page.table.actions.requestCompleted");
    }
    if (hasActed) {
      return userAction === "approve" 
        ? t("people.approval-requests-page.table.actions.alreadyApproved")
        : t("people.approval-requests-page.table.actions.alreadyRejected");
    }
    return null;
  };

  const statusMessage = getStatusMessage();

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem
          onClick={() => onShowDetails(approvalRequest)}
          className="cursor-pointer"
        >
          <Eye className="mr-2 h-4 w-4" />
          {t("people.approval-requests-page.table.actions.viewDetails")}
        </DropdownMenuItem>
        
        {/* Show status message if user has already acted or request is completed */}
        {statusMessage && (
          <DropdownMenuItem disabled className="text-gray-500 text-sm">
            {statusMessage}
          </DropdownMenuItem>
        )}
        
        {/* Only show action buttons if user can act and hasn't acted yet */}
        {!shouldDisableActions && (
          <>
            <DropdownMenuItem
              onClick={() => onAccept(approvalRequest)}
              className="cursor-pointer text-green-600"
            >
              <Check className="mr-2 h-4 w-4" />
              {t("people.approval-requests-page.table.actions.approve")}
            </DropdownMenuItem>
            
            <DropdownMenuItem
              onClick={() => onReject(approvalRequest)}
              className="cursor-pointer text-red-600"
            >
              <X className="mr-2 h-4 w-4" />
              {t("people.approval-requests-page.table.actions.reject")}
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
