"use client";

import { useTranslations } from "next-intl";
import { TFunction } from "@/types";
import { useDevice } from "../../../hooks/devices/useDevice";
import { useLocale } from "next-intl";
import { Button } from "@/components/ui/button";
import { formatDate } from "@/lib/dateFormatter";
import { mapDeviceStatusToCanonical } from "@/constants/translations-mapping";
import {
  Edit,
  Refresh,
  Trash,
} from "../../../../../../../../public/images/icons";
import { Loader2 } from "lucide-react";
import { useState } from "react";
import { TDevice } from "../../../type/devices/device";
import dynamic from "next/dynamic";
import LoaderPortal from "@/components/loader/loader-portal";
import DeviceDetailsHeaderSkeleton from "./device-details-header-skeleton";
import { useRouter } from "next/navigation";

const DeviceActionsDialogs = dynamic(
  () => import("../shared/device-actions-dialogs"),
  {
    ssr: false,
    loading: () => <LoaderPortal overlayColor="#000" overlayOpacity={0.6} />,
  },
);

type DeviceDetailsHeaderProps = {
  deviceId: string;
};

export default function DeviceDetailsHeader({
  deviceId,
}: DeviceDetailsHeaderProps) {
  const t = useTranslations() as TFunction;
  const locale = useLocale();
  const router = useRouter();

  const {
    data: device,
    isLoading,
    error,
    refreshDevice,
    updateDevice,
    deleteDevice,
    isRefreshing,
  } = useDevice(deviceId);

  // Device actions state
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const actionsState = {
    isEditDialogOpen,
    isDeleteDialogOpen,
    selectedDevice: device || null,
  };

  const actionsHandlers = {
    handleEdit: (_device: TDevice) => {
      setIsEditDialogOpen(true);
    },
    handleDelete: (_device: TDevice) => {
      setIsDeleteDialogOpen(true);
    },
    handleConfirmDelete: async () => {
      const success = await deleteDevice();
      if (success) {
        setIsDeleteDialogOpen(false);
        // Redirect to devices list after successful deletion
        router.push(`/${locale}/people/devices`);
      }
    },
    handleUpdateSuccess: (updatedDevice: TDevice) => {
      updateDevice(updatedDevice);
      setIsEditDialogOpen(false);
    },
    closeEditDialog: () => {
      setIsEditDialogOpen(false);
    },
    closeDeleteDialog: () => {
      setIsDeleteDialogOpen(false);
    },
  };

  if (isLoading) {
    return <DeviceDetailsHeaderSkeleton />;
  }

  if (error || !device) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="text-start text-red-500">
          {t("people.devices-page.device-details.error-loading")}
        </div>
      </div>
    );
  }

  // Use device attributes (now that we know the structure)
  const deviceData = device.attributes;

  const canonicalStatus = mapDeviceStatusToCanonical(device.attributes.status);
  const translatedStatus = t(`common.status.device-status.${canonicalStatus}`);

  // Metrics data configuration
  const metrics = [
    {
      label: t("people.devices-page.device-details.metrics.ip-address"),
      value: deviceData.ip_address,
    },
    {
      label: t("people.devices-page.device-details.metrics.port"),
      value: deviceData.port,
    },
    {
      label: t("people.devices-page.device-details.metrics.created-date"),
      value: formatDate(deviceData.created_at, locale),
    },
    {
      label: t("people.devices-page.device-details.metrics.status"),
      value: translatedStatus,
      className: "text-green-700",
    },
  ];

  return (
    <div className="bg-white rounded-2xl border border-gray-200">
      <div className="flex flex-col  sm:flex-row items-center justify-center sm:justify-between gap-6 sm:gap-4 p-6 flex-wrap">
        <div className="flex items-center flex-col gap-3 shrink-0 max-sm:border-b sm:border-l pb-5 sm:pl-10">
          <h1 className="text-xl font-bold text-gray-900">{deviceData.name}</h1>
          <span className="text-gray-500">{deviceData.adapter_type}</span>
        </div>

        {/* Metrics Row */}
        <div className="grid max-sm:w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-4 justify-between items-center w-[60%] gap-8 sm:gap-4">
          {metrics.map((metric, index) => (
            <div key={index} className="text-center lg:text-start">
              <div className="text-xs text-gray-400 font-medium leading-5 mb-2">
                {metric.label}
              </div>
              <div
                className={`text-sm font-medium leading-5 tracking-[0.5%] ${
                  metric.className || "text-gray-900"
                }`}
              >
                {metric.value}
              </div>
            </div>
          ))}
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            className="w-10 h-10 text-gray-600 hover:text-gray-700 hover:bg-gray-50"
            onClick={refreshDevice}
            disabled={isRefreshing}
          >
            {isRefreshing ? (
              <Loader2 className="!w-5 !h-5 animate-spin" />
            ) : (
              <Refresh className="!w-5 !h-5" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-10 h-10 text-gray-600 hover:text-gray-700 hover:bg-gray-50"
            onClick={() => actionsHandlers.handleEdit(device)}
          >
            <Edit className="!w-5 !h-5" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="w-10 h-10 text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={() => actionsHandlers.handleDelete(device)}
          >
            <Trash className=" !w-6 !h-6" />
          </Button>
        </div>
      </div>

      {/* Device Actions Dialogs */}
      {actionsState && (
        <DeviceActionsDialogs state={actionsState} handlers={actionsHandlers} />
      )}
    </div>
  );
}
