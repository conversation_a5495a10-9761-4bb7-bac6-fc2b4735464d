"use client";

import { DialogDescription, DialogTitle } from "@/components/ui/dialog";
import ResponsiveDialog from "@/components/responsive-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Edit2 } from "../../../../../../../public/images/icons";
import { useTranslations } from "next-intl";
import FormattedCurrency from "@/components/formatted-currency";
import { useUnitFormatter } from "@/hooks/use-unit-formatter";
import { TEmployeesSalaries } from "../../type/employees-salaries";
import { mapSalaryStatusToCanonical } from "@/constants/translations-mapping";
import { useUpdateSalaryStatusMutations } from "../../hooks/useUpdateSalaryMutations";
import { useSearchParams } from "next/navigation";
import { useToastMessage } from "@/hooks/use-toast-message";

type TEmployeesSalariesDetails = {
  showSalariesDetails: boolean;
  setShowSalariesDetails: React.Dispatch<React.SetStateAction<boolean>>;
  setShowEditSalaryDialog: React.Dispatch<React.SetStateAction<boolean>>;
  selectedEmployee: TEmployeesSalaries;
};

const EmployeeRequestsDetailsDialog = ({
  showSalariesDetails,
  setShowSalariesDetails,
  setShowEditSalaryDialog,
  selectedEmployee,
}: TEmployeesSalariesDetails) => {
  return (
    <ResponsiveDialog
      open={showSalariesDetails}
      onOpenChange={setShowSalariesDetails}
      closeBtnStyle="top-[22px]"
      header={<RequestDetailsHeader />}
    >
      <RequestDetailsContent
        showSalariesDetails={showSalariesDetails}
        setShowSalariesDetails={setShowSalariesDetails}
        setShowEditSalaryDialog={setShowEditSalaryDialog}
        selectedEmployee={selectedEmployee}
      />
    </ResponsiveDialog>
  );
};

const RequestDetailsHeader = () => {
  const t = useTranslations();
  return (
    <div className="text-start">
      <DialogDescription className="sr-only">
        {t(
          "people.employees-salaries-page.employee-salary-dialog.header.description",
        )}
      </DialogDescription>
      <div className="px-6 py-[22px] border-b ">
        <div className="font-semibold flex items-center justify-start gap-3">
          <DialogTitle className="!m-0 text-[18px] font-semibold leading-7">
            {t(
              "people.employees-salaries-page.employee-salary-dialog.header.title",
            )}
          </DialogTitle>
        </div>
      </div>
    </div>
  );
};

const RequestDetailsContent = ({
  setShowSalariesDetails,
  setShowEditSalaryDialog,
  selectedEmployee,
}: TEmployeesSalariesDetails) => {
  const t = useTranslations();
  const formatUnit = useUnitFormatter();
  const searchParams = useSearchParams();
  const limit = searchParams.get("limit") ?? 3;
  const page = searchParams.get("page") ?? 1;
  const { showToast } = useToastMessage();
  const { data, isLoading, error, mutateData } = useUpdateSalaryStatusMutations(
    {
      key: `/api/data/salaries?page=${page}&limit=${limit}`,
    },
  );

  const onEditSalary = () => {
    setShowSalariesDetails(false);
    setShowEditSalaryDialog((prev) => !prev);
  };

  // Store formatted values before JSX
  const leaveDays = formatUnit(selectedEmployee.numberOfLeaves, "day");
  const workHours = formatUnit(selectedEmployee.totalHours, "hour");
  const overtimeHours = formatUnit(selectedEmployee.overtimeHours, "hour");

  const canonicalStatus = mapSalaryStatusToCanonical(selectedEmployee.status);

  return (
    <div className="bg-white w-full">
      <div className="flex justify-between items-center border rounded-2xl p-6">
        <InfoItem
          label={t(
            "people.employees-salaries-page.employee-salary-dialog.info.clientNameLabel",
          )}
          value={selectedEmployee.employeeName}
        />
        <InfoItem
          label={t(
            "people.employees-salaries-page.employee-salary-dialog.info.monthLabel",
          )}
          value={selectedEmployee.month}
        />
        <InfoItem
          label={t(
            "people.employees-salaries-page.employee-salary-dialog.info.leavesLabel",
          )}
          value={`${leaveDays.numberPart} ${leaveDays.unitPart}`}
        />
      </div>

      <div className="border rounded-2xl p-6 mt-4">
        <div className="pb-5 border-b border-gray-100 space-y-5">
          <DataItem
            label={t(
              "people.employees-salaries-page.employee-salary-dialog.data.workHoursLabel",
            )}
            value={workHours.numberPart}
            unit={workHours.unitPart}
          />
          <DataItem
            label={t(
              "people.employees-salaries-page.employee-salary-dialog.data.overtimeLabel",
            )}
            value={overtimeHours.numberPart}
            unit={overtimeHours.unitPart}
          />
        </div>

        <div className="my-5">
          <DataItem
            label={t(
              "people.employees-salaries-page.employee-salary-dialog.data.hourlyRateLabel",
            )}
            value={selectedEmployee.hourlyRate}
            isCurrency={true}
          />
        </div>

        <div className="rounded-2xl border-t border-gray-100 pt-5 space-y-5">
          <DataItem
            label={t(
              "people.employees-salaries-page.employee-salary-dialog.data.discountLabel",
            )}
            value={selectedEmployee.totalDiscount}
            isCurrency={true}
          />
          <DataItem
            label={t(
              "people.employees-salaries-page.employee-salary-dialog.data.taxLabel",
            )}
            value={selectedEmployee.tax}
            isCurrency={true}
          />
        </div>

        <div className="relative text-[18px] px-2 font-readex_pro mb-10 bg-[hsl(var(--background-v2),0.5)] py-5 mt-4 flex justify-between items-center border-secondary bg-dashed-border">
          <span className="font-medium text-secondary leading-7">
            {t(
              "people.employees-salaries-page.employee-salary-dialog.total.label",
            )}
          </span>
          <span className="font-semibold leading-7 flex items-center gap-2 text-[18px]">
            <Button onClick={onEditSalary} variant="ghost" className="p-0">
              <Edit2 className="!w-6 !h-6 m-0 !p-0" />
            </Button>
            <FormattedCurrency
              wrapperStyle="text-[18px] gap-2"
              numberStyle="font-bold text-secondary"
              currencyStyle="text-black"
              amount={selectedEmployee.totalSalary}
            />
          </span>
        </div>

        <div className="flex flex-col font-readex_pro">
          <span className="leading-6 text-base text-gray-500 font-medium">
            {t("people.leaves-requests-page.dialog-common.note-label")}
          </span>
          <p className="text-gray-900 leading-6 font-semibold">
            هذا النص هو مثال لنص يمكن أن يستبدل في نفس المساحة.
          </p>
        </div>
      </div>
      {canonicalStatus === "draft" && (
        <div className="flex fixed bottom-0 start-0 p-4 sm:p-6 w-full gap-6 bg-white justify-between mt-10 rounded-b-2xl pt-28">
          <Button
            disabled={isLoading}
            onClick={() =>
              mutateData(`updateSalaryStatus`, {
                id: selectedEmployee.id,
                status: "paid",
              })
                .then(() => {
                  showToast(
                    "success",
                    t("common.Error.responses.paymentConfirmed"),
                  );
                  setShowSalariesDetails(false);
                })
                .catch(() => {
                  showToast(
                    "error",
                    t("common.Error.responses.paymentConfirmFailed"),
                  );
                })
            }
            className=" text-white w-full h-12"
          >
            {t("common.buttonText.payment-confirm")}
          </Button>
          <Button
            onClick={() => setShowSalariesDetails(false)}
            variant={"outline"}
            className="border h-12 w-full font-semibold bg-gray-50"
          >
            {t("common.buttonText.cancel2")}
          </Button>
        </div>
      )}
    </div>
  );
};

const InfoItem = ({ label, value }: { label: string; value: string }) => (
  <div className="flex flex-col justify-between gap-1">
    <span className="font-medium text-gray-400 text-sm leading-5 tracking-[0.5%]">
      {label}
    </span>
    <span className="font-medium text-gray-900 text-sm leading-5 tracking-[0.5%]">
      {value}
    </span>
  </div>
);

const DataItem = ({
  label,
  value,
  unit,
  isCurrency = false,
}: {
  label: string;
  value: number | string;
  unit?: string;
  isCurrency?: boolean;
}) => {
  const baseTextClass =
    "flex gap-1.5 flex-row text-base font-medium leading-6 font-readex_pro";
  return (
    <div className="flex justify-between">
      <span className="text-gray-500 leading-6 font-medium text-base font-readex_pro">
        {label}
      </span>
      {isCurrency ? (
        <FormattedCurrency
          wrapperStyle={baseTextClass}
          numberStyle="font-semibold text-gray-900"
          currencyStyle="text-gray-400"
          amount={Number(value)}
        />
      ) : (
        <span className={baseTextClass}>
          <span className="font-semibold text-gray-900">{value}</span>
          <span className="text-gray-400">{unit}</span>
        </span>
      )}
    </div>
  );
};

export default EmployeeRequestsDetailsDialog;
