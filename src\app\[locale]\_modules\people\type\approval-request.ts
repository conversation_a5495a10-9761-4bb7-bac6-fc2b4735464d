// Type definitions for approval requests

export type ApprovalAction = {
  id: number;
  user_id: string;
  action: "approve" | "reject";
  comment: string;
  created_at: string;
};

export type ApprovalStep = {
  id: number;
  name: string;
  sequence: number;
  approval_type: string;
  complete: boolean;
  rejected: boolean;
  actions: ApprovalAction[];
};

export type Approvable = {
  id: number;
  type: string;
};

export type Requestor = {
  id: number;
  name: string;
};

export type ApprovalRequestAttributes = {
  workflow_id: string;
  workflow_name: string;
  status: "pending" | "approved" | "rejected";
  created_at: string;
  updated_at: string;
  approvable: Approvable;
  requestor: Requestor;
  current_step: ApprovalStep | null;
  steps: ApprovalStep[];
};

export type ApprovalRequestData = {
  id: string;
  type: "approval_request";
  attributes: ApprovalRequestAttributes;
};

export type ApprovalRequestResponse = {
  data: ApprovalRequestData;
};

// Type for the list response from the API
export type ApprovalRequestsResponse = {
  data: ApprovalRequestData[];
  meta: {
    pagination: {
      count: number;
      page: number;
      limit: number;
      from: number;
      to: number;
    };
  };
};

// Type for the response from our server actions
export type ApprovalActionResponse = {
  data?: {
    id: string;
    status: string;
    updatedAt: string;
    approvalRequest?: ApprovalRequestData;
  };
  error?: Error;
};
