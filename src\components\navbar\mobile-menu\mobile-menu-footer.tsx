"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { TbSettings2 } from "react-icons/tb";
import { useTranslations } from "next-intl";
import { useSettingsModal } from "@/contexts/settings-modal-context";
import { LanguageSwitcher } from "@/components/local-switcher";
import { useMobileMenu } from "@/contexts/mobile-menu-context";
import { onSubmitLogout } from "@/server/actions/auth";
import { useActionState, startTransition } from "react";
import { useState } from "react";
import { ActionState } from "@/types";
import { useUser } from "@/contexts/user-provider";
import { SignOut } from "../../../../public/images/icons";
import SelectSystemModal from "@/components/sidebar/select-system-modal";
import SystemSelector from "@/components/system/system-selector";
import { usePermission } from "@/contexts/PermissionContext";
import { performLogout } from "@/utils/logout";

const MobileMenuFooter: React.FC = () => {
  const t = useTranslations();
  const { openSettings } = useSettingsModal();
  const { closeMenu } = useMobileMenu();
  const { mutateUser } = useUser();
  const { clearPermissions } = usePermission();
  const [openSelectModal, setOpenSelectModal] = useState<boolean>(false);

  // Initial state for logout action
  const initialState: ActionState<{ redirectTo: string }> = {
    error: "",
    success: "",
    issues: [],
    redirectTo: "",
  };

  const [, logoutAction, isPending] = useActionState(
    onSubmitLogout,
    initialState,
  );

  // Logout functionality
  const handleLogout = async () => {
    closeMenu();

    await performLogout(clearPermissions, mutateUser, () => {
      startTransition(() => {
        logoutAction();
      });
    });
  };

  const handleOpenSettings = (
    tab: "GENERAL" | "NOTIFICATIONS",
    subForm?: "EDIT_PROFILE" | "CHANGE_PASSWORD",
  ) => {
    closeMenu();
    openSettings(tab, subForm);
  };

  return (
    <div className="mt-4 space-y-6 pb-8">
      <div className="my-4 border-t" />

      {/* Settings Button */}
      <Button
        onClick={() => handleOpenSettings("GENERAL", "EDIT_PROFILE")}
        variant="ghost"
        className="w-full justify-start text-[#6B7271] hover:text-black hover:bg-[hsl(var(--primary),0.2)] rounded-full"
      >
        <TbSettings2 className=" !h-6 !w-6" />
        <span>{t("common.sidebar.settings")}</span>
      </Button>

      {/* Language Switcher */}
      <div className="flex items-center justify-between px-2">
        <span className="text-sm font-medium text-secondary">
          {t("common.navbar.mobileMenu.language")}
        </span>
        <LanguageSwitcher />
      </div>

      {/* System Selector */}
      <SystemSelector
        className="w-full"
        onClick={() => {
          setOpenSelectModal(true);
        }}
        showIcon={true}
      />

      {/* System Selection Modal */}
      {openSelectModal && (
        <SelectSystemModal
          openModal={openSelectModal}
          setOpenModal={setOpenSelectModal}
        />
      )}
      <div>
        {/* Logout Button */}
        <Button
          onClick={handleLogout}
          variant="ghost"
          className="w-full justify-start text-error rounded-full h-[50px] !mt-12"
          disabled={isPending}
        >
          <SignOut className=" h-6 w-6" />
          <span>{t("common.navbar.userProfile.logout")}</span>
        </Button>
      </div>
    </div>
  );
};

export default MobileMenuFooter;
