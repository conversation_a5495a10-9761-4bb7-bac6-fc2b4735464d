"use client";

import { useTranslations } from "next-intl";
import { useEmployees } from "../hooks/employees/useEmployees";
import { useEmployeeDetails } from "../hooks/employees/useEmployeeDetails";
import { EmployeeProfileHeader } from "./employees/profile/employee-profile-header";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import { useLocale } from "next-intl";
import { Locale } from "@/i18n/routing";
import { EmployeeAttendanceCards } from "./employees/profile/attendance/employee-attendance-cards";
import { EmployeeLeavesCards } from "./employees/profile/employee-leaves-cards";

import EmployeeAttachments from "./employees/profile/attachments";
import { EmployeeLeavesTable } from "./employees/profile/leaves";
import EmployeeRolesProjects from "./employees/profile/roles-projects";
import { EmployeeProfileHeaderSkeleton } from "../skeletons/employee-profile-header-skeleton";
import { Skeleton } from "@/components/ui/skeleton";

type TabKey = "attendance" | "attachments" | "leaves" | "roles-projects";

const EmployeeProfileMainView = () => {
  const t = useTranslations();
  const currentLocale: Locale = useLocale() as Locale;
  const isAr = currentLocale === "ar";

  // Get the single employee ID using useEmployees hook
  // When user has only READ_OWN_EMPLOYEE permission, this will return array with one employee
  const {
    employees,
    isLoading: employeesLoading,
    error: employeesError,
  } = useEmployees(1, 1);

  const employeeId = employees?.[0]?.id || employees?.[0]?.attributes?.user_id;

  // Get detailed employee data
  const {
    employee,
    employeeData,
    userRoles,
    isLoading: detailsLoading,
    error: detailsError,
  } = useEmployeeDetails(employeeId?.toString() || "");

  const isLoading = employeesLoading || detailsLoading;
  const error = employeesError || detailsError;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <EmployeeProfileHeaderSkeleton />
        <div className="bg-white rounded-[20px] border border-gray-200 p-6">
          <Skeleton className="h-[400px] w-full bg-gray-200" />
        </div>
      </div>
    );
  }

  if (error || !employee || !employeeId) {
    return (
      <div className="bg-white rounded-[20px] border border-gray-200 p-6">
        <div className="flex flex-col gap-6">
          <h2 className="text-xl font-semibold text-red-500">
            {t("common.Error.title")}
          </h2>
          <p className="text-gray-700">
            {error?.message || "Employee not found"}
          </p>
        </div>
      </div>
    );
  }

  const tabs: TabKey[] = [
    "attendance",
    "leaves",
    "attachments",
    "roles-projects",
  ];

  const tabTriggerClass =
    "text-base font-medium data-[state=active]:bg-transparent data-[state=active]:text-secondary data-[state=active]:shadow-none data-[state=active]:border-b-4 border-secondary rounded-none text-gray-400 pb-[14px] data-[state=active]:font-bold max-w-36 max-lg:px-0.5";

  return (
    <div className="space-y-6 max-md:mt-6">
      {/* Employee Profile Header */}
      <EmployeeProfileHeader
        employee={employee}
        employeeData={employeeData}
        userRoles={userRoles || []}
      />

      {/* Navigation Tabs */}
      <div className="bg-none border-b pb-5 border-gray-200 !mt-4 sm:!mt-8">
        <Tabs
          defaultValue="attendance"
          className="overflow-auto overflow-y-hidden custom-scroll"
          dir={isAr ? "rtl" : "ltr"}
        >
          <TabsList className="flex justify-start border-b gap-10 py-[22px] grid-cols-5 bg-transparent max-w-full w-full">
            {tabs.map((tab) => (
              <TabsTrigger key={tab} value={tab} className={tabTriggerClass}>
                {t(`people.employees-page.profile.tabs.${tab}`)}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Tab Contents */}
          <TabsContent value="attendance" className="!mt-5">
            <EmployeeAttendanceCards employeeId={employeeId.toString()} />
          </TabsContent>

          <TabsContent value="leaves" className="!mt-5 space-y-6">
            <EmployeeLeavesCards employeeId={employeeId.toString()} />
            <EmployeeLeavesTable employeeId={employeeId.toString()} />
          </TabsContent>

          <TabsContent value="attachments" className="!mt-5">
            <EmployeeAttachments employeeId={employeeId.toString()} />
          </TabsContent>

          <TabsContent value="roles-projects" className="!mt-5">
            <EmployeeRolesProjects employeeId={employeeId.toString()} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default EmployeeProfileMainView;
