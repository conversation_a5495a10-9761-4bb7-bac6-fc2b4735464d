"use client";

import { SWRConfig } from "swr";
import { fetcher } from "@/services/fetcher";
const SWRProvider = ({ children }: { children: React.ReactNode }) => {
  return (
    <SWRConfig
      value={{
        fetcher,
        revalidateOnReconnect: true,
        dedupingInterval: 2000,
        revalidateOnFocus: false,
        errorRetryCount: 3,
      }}
    >
      {children}
    </SWRConfig>
  );
};

export default SWRProvider;
